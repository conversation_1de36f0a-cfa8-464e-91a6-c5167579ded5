{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["resources/js/src/*"], "@components/*": ["resources/js/src/components/*"], "@layouts/*": ["resources/js/src/layouts/*"], "@pages/*": ["resources/js/src/pages/*"], "@store/*": ["resources/js/src/store/*"], "@hooks/*": ["resources/js/src/hooks/*"], "@utils/*": ["resources/js/src/utils/*"], "@assets/*": ["resources/js/src/assets/*"], "@css/*": ["resources/js/src/assets/css/*"], "@images/*": ["resources/js/src/assets/images/*"]}}, "include": ["resources/js/src"], "references": [{"path": "./tsconfig.node.json"}]}