<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AppointmentApprovedMail extends Mailable
{
    use Queueable, SerializesModels;

    public $appointment;

    public $customer;

    public $branch;

    public $staff;

    /**
     * Create a new message instance.
     */
    public function __construct($appointment, $customer, $branch, $staff = null)
    {
        $this->appointment = $appointment;
        $this->customer    = $customer;
        $this->branch      = $branch;
        $this->staff       = $staff;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Your Appointment is Approved!')
            ->view('emails.appointment_approved');
    }
}
