<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Appointment;
use App\Models\AppointmentService;
use App\Models\PlanServiceUsage;
use App\Models\PlanUsage;
use App\Models\Tenant;

class UserDashboardService
{
    public function getDashboardData($user): array
    {

        // Only allow if user is customer
        if (! $user->hasRole('customer')) {
            return [
                'totalAppointments'    => 0,
                'totalPaid'            => 0,
                'appointmentHistory'   => [],
                'todaysAppointments'   => [],
                'incomingAppointments' => [],
                'planHistory'          => [],
                'activePlan'           => null,
            ];
        }

        $today = now()->toDateString();

        // Total appointments for this user in this tenant's branches
        $totalAppointments = Appointment::where('user_id', $user->id)->count();

        // Total paid (sum of all completed appointment services for the user in these branches)
        $totalPaid = AppointmentService::whereHas('appointment', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->where('status', 'completed')->sum('price');

        // Appointment history (all appointments for this user in these branches)
        $appointmentHistory = Appointment::with('branch', 'appointmentServices')
            ->where('user_id', $user->id)
            ->orderByDesc('id')
            ->get()
            ->map(function ($a) {
                $getplanService   = [];
                $getServiceDetail = [];
                foreach ($a->appointmentServices as $servcedetails) {
                    if ($servcedetails->plan_used_service) {
                        $getplanService[] = $servcedetails->plan_used_service;
                    }
                    $getServiceDetail[] = [
                        'appointment_service'                   => $servcedetails->service_name,
                        'appointment_service_price'             => $servcedetails->price,
                        'appointment_service_notes'             => $servcedetails->service_notes,
                        'appointment_service_plan_used_service' => $servcedetails->plan_used_service,
                        'appointment_service_service_id'        => $servcedetails->service_id,
                    ];

                }

                $planUsages = PlanServiceUsage::whereIn('plan_service_usages.id', $getplanService)
                    ->join('plan_usages', 'plan_usages.id', '=', 'plan_service_usages.plan_usage_id')
                    ->join('plans', 'plan_usages.plan_id', '=', 'plans.id')
                    ->select('plans.name', 'service_name', 'service_id')->get()->toArray();

                return [
                    'id'                  => $a->id,
                    'branch_name'         => $a->branch->name ?? '',
                    'date'                => $a->appointment_date ? $a->appointment_date->format('Y-m-d') : '',
                    'time'                => $a->appointment_time,
                    'status'              => $a->status,
                    'currency_symbol'     => $a->currency_symbol,
                    'currency_text'       => $a->currency_text,
                    'total_paid'          => $a->appointmentServices->sum('price'),
                    'ticket_number'       => $a->ticket_number,
                    'staff_json'          => $a->staff_json,
                    'appoinmentUsedPlan'  => $planUsages,
                    'appointmentServices' => $getServiceDetail,

                ];
            });

        // Today's appointments
        $todaysAppointments = $appointmentHistory->filter(fn ($a) => $a['date'] === $today)->values();

        // Incoming appointments (future appointments, status pending or in_progress)
        $incomingAppointments = $appointmentHistory->filter(fn ($a) => $a['date'] > $today && in_array($a['status'], ['pending', 'in_progress']))->values();

        // Plan history (for this user in these branches)
        $planHistory = PlanUsage::with('plan')
            ->where('user_id', $user->id)
            ->orderByDesc('purchased_at')
            ->get()
            ->map(function ($p) {
                return [
                    'id'                => $p->id,
                    'name'              => $p->plan->name          ?? '',
                    'price'             => $p->plan->price         ?? 0,
                    'validity_days'     => $p->plan->validity_days ?? 0,
                    'status'            => $p->status,
                    'currency_symbol'   => $p->currency_symbol,
                    'currency_text'     => $p->currency_text,
                    'staff_json'        => $p->staff_json,
                    'purchased_at'      => $p->purchased_at ? $p->purchased_at->format('Y-m-d') : '',
                    'expires_at'        => $p->expires_at ? $p->expires_at->format('Y-m-d') : '',
                    'planServiceUsages' => $p->planServiceUsages->map(function ($serviceUsage) {
                        return [
                            'id'      => $serviceUsage->id,
                            'service' => [
                                'id'   => $serviceUsage->service->id,
                                'name' => ($serviceUsage->service_name) ? $serviceUsage->service_name : $serviceUsage->service->name,
                            ],
                            'remaining_count' => $serviceUsage->remaining_count,
                            'used_count'      => $serviceUsage->used_count,
                        ];
                    }),
                ];
            });

        // Active plan (first active and not expired for this user in these branches)
        $activePlan = PlanUsage::with('plan')
            ->where('user_id', $user->id)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->orderByDesc('expires_at')
            ->first();
        $activePlanArr = $activePlan ? [
            'id'            => $activePlan->id,
            'name'          => $activePlan->plan->name          ?? '',
            'price'         => $activePlan->plan->price         ?? 0,
            'validity_days' => $activePlan->plan->validity_days ?? 0,
            'expires_at'    => $activePlan->expires_at ? $activePlan->expires_at->format('Y-m-d') : '',
        ] : null;

        return [
            'totalAppointments'    => $totalAppointments,
            'totalPaid'            => $totalPaid,
            'appointmentHistory'   => $appointmentHistory,
            'todaysAppointments'   => $todaysAppointments,
            'incomingAppointments' => $incomingAppointments,
            'planHistory'          => $planHistory,
            'activePlan'           => $activePlanArr,
        ];
    }
}
