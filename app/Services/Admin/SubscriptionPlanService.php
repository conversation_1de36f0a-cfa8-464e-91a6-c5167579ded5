<?php

declare(strict_types=1);

namespace App\Services\Admin;

use App\Models\SubscriptionPlan;
use App\Models\VendorSubscription;
use Illuminate\Validation\ValidationException;

class SubscriptionPlanService
{
    protected array $fields = [
        'id',
        'name',
        'description',
        'price',
        'currency_symbol',
        'currency_code',
        'billing_cycle',
        'max_services',
        'max_branches',
        'max_staff',
        'is_active',
        'sort_order',
    ];

    public function list()
    {
        return SubscriptionPlan::select($this->fields)->orderBy('sort_order')->orderBy('price')->get();
    }

    public function create(array $data): SubscriptionPlan
    {
        return SubscriptionPlan::create($data);
    }

    public function find($id): ?SubscriptionPlan
    {
        return SubscriptionPlan::findOrFail($id);
    }

    public function update($id, array $data): bool
    {
        $plan = SubscriptionPlan::findOrFail($id);

        return $plan->update($data);
    }

    public function delete($id): bool
    {
        $plan = SubscriptionPlan::findOrFail($id);
        // Check for active or non-expired vendor subscriptions
        $hasActiveOrNonExpired = VendorSubscription::where('subscription_plan_id', $plan->id)
            ->where(function ($q) {
                $q->where('status', 'active')
                    ->orWhere(function ($q2) {
                        $q2->where('ends_at', '>', now());
                    });
            })
            ->exists();
        if ($hasActiveOrNonExpired) {
            throw ValidationException::withMessages([
                'error' => 'Cannot delete: This plan has active or non-expired vendor subscriptions.',
            ]);
        }

        return $plan->delete();
    }

    public function trashed()
    {
        return SubscriptionPlan::onlyTrashed()->get();
    }

    public function restore($id): bool
    {
        $plan = SubscriptionPlan::onlyTrashed()->findOrFail($id);

        return $plan->restore();
    }
}
