<?php

declare(strict_types=1);

namespace App\Services\Admin;

use App\Models\NotificationChannel;

class NotificationChannelService
{
    public function list()
    {
        return NotificationChannel::all();
    }

    public function find($id): ?NotificationChannel
    {
        return NotificationChannel::findOrFail($id);
    }

    public function create(array $data): array
    {
        // Check if a soft-deleted record with the same name exists
        $existingTrashed = NotificationChannel::onlyTrashed()
            ->where('name', $data['name'])
            ->first();

        if ($existingTrashed) {
            $existingTrashed->restore();
            $existingTrashed->update($data);

            return [
                'model'        => $existingTrashed,
                'was_restored' => true,
            ];
        }

        return [
            'model'        => NotificationChannel::create($data),
            'was_restored' => false,
        ];
    }

    public function update($id, array $data): bool
    {
        $channel = NotificationChannel::findOrFail($id);

        return $channel->update($data);
    }

    public function delete($id): bool
    {
        $channel = NotificationChannel::findOrFail($id);

        return $channel->delete();
    }

    public function trashed()
    {
        return NotificationChannel::onlyTrashed()->get();
    }

    public function restore($id): bool
    {
        $channel = NotificationChannel::onlyTrashed()->findOrFail($id);

        return $channel->restore();
    }
}
