<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Scopes\TenantScope;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

final class Appointment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'branch_id',
        'appointment_date',
        'appointment_time',
        'ticket_number',
        'currency_symbol',
        'currency_text',
        'staff_id',
        'staff_json',
        'status',
        'notes',
    ];

    protected $casts = [
        'appointment_date' => 'date',
        'status'           => 'string',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function appointmentServices(): HasMany
    {
        return $this->hasMany(AppointmentService::class);
    }

    public function services(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'appointment_services')
            ->withPivot(['status', 'start_time', 'end_time', 'estimated_end_time', 'service_notes'])
            ->withTimestamps();
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    protected static function booted(): void
    {
        self::addGlobalScope(new TenantScope);
    }

    public function updatetimeonProcess($appoinment_id, $service_id, $status)
    {
        $serviceQry   = Service::find($service_id);
        $durationTime = $serviceQry->duration_minutes;
        $now          = Carbon::now();
        $updateData   = [];
        if ($status == 'in_progress') {
            $updateData = [
                'start_time'         => $now->copy(),
                'estimated_end_time' => $now->copy()->addMinutes($durationTime),
                'status'             => $status,
            ];
        } elseif ($status == 'completed') {
            $updateData = [
                'end_time' => $now->copy(),
                'status'   => $status,
            ];
        } elseif ($status == 'pending') {
            $updateData = [
                'start_time'         => null,
                'end_time'           => null,
                'estimated_end_time' => null,
                'status'             => $status,
            ];
        }

        if (! empty($updateData)) {

            $this->appointmentServices()
                ->where('appointment_id', $appoinment_id)
                ->where('service_id', $service_id)
                ->update($updateData);

        }

    }
}
