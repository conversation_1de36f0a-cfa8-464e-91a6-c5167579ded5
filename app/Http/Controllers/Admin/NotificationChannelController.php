<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\NotificationChannelService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class NotificationChannelController extends Controller
{
    private NotificationChannelService $service;

    public function __construct(NotificationChannelService $service)
    {
        $this->service = $service;
    }

    public function index()
    {
        $channels     = $this->service->list();
        $trashedCount = $this->service->trashed()->count();

        return Inertia::render('Admin/NotificationChannels/Index', [
            'channels'     => $channels,
            'trashedCount' => $trashedCount,
        ]);
    }

    public function create()
    {
        return Inertia::render('Admin/NotificationChannels/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name'      => 'required|string|max:255',
            'driver'    => 'required|string|max:255',
            'config'    => 'required|array',
            'is_active' => 'required|boolean',
        ]);

        $result = $this->service->create($validated);

        $message = $result['was_restored']
            ? 'Notification channel restored and updated successfully.'
            : 'Notification channel added successfully.';

        return redirect()->route('siteadmin.notification-channels.index')->with('success', $message);
    }

    public function edit($id)
    {
        $channel = $this->service->find($id);

        return Inertia::render('Admin/NotificationChannels/Edit', [
            'channel' => $channel,
        ]);
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name'      => 'required|string|max:255',
            'driver'    => 'required|string|max:255',
            'config'    => 'required|array',
            'is_active' => 'required|boolean',
        ]);

        $this->service->update($id, $validated);

        return redirect()->route('siteadmin.notification-channels.index')->with('success', 'Notification channel updated successfully.');
    }

    public function destroy($id)
    {
        $this->service->delete($id);

        return redirect()->route('siteadmin.notification-channels.index')->with('success', 'Notification channel deleted successfully.');
    }

    public function trashed()
    {
        $trashed = $this->service->trashed();

        return Inertia::render('Admin/NotificationChannels/Trashed', [
            'channels' => $trashed,
        ]);
    }

    public function restore($id)
    {
        $this->service->restore($id);

        return redirect()->route('siteadmin.notification-channels.trashed')->with('success', 'Notification channel restored successfully.');
    }
}
