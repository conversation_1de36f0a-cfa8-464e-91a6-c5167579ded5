<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Rules\UniquePaymentMethodType;
use App\Services\Admin\PaymentMethodService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PaymentMethodController extends Controller
{
    private PaymentMethodService $service;

    public function __construct(PaymentMethodService $service)
    {
        $this->service = $service;
    }

    public function index()
    {
        $methods      = $this->service->list();
        $trashedCount = $this->service->trashed()->count();

        return Inertia::render('Admin/PaymentMethods/Index', [
            'methods'      => $methods,
            'trashedCount' => $trashedCount,
        ]);
    }

    public function create()
    {
        return Inertia::render('Admin/PaymentMethods/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name'    => 'required|string|max:255',
            'type'    => ['required', 'string', 'max:255', new UniquePaymentMethodType],
            'mode'    => 'required|in:live,test',
            'details' => 'required|array',
            'status'  => 'required|in:active,deactive',
        ]);

        $result = $this->service->create($validated);

        // Check if it was restored from trash
        $message = $result['was_restored']
            ? 'Payment method restored and updated successfully.'
            : 'Payment method added successfully.';

        return redirect()->route('siteadmin.payment-methods.index')->with('success', $message);
    }

    public function show($id)
    {
        $method = $this->service->find($id);

        return Inertia::render('Admin/PaymentMethods/Show', [
            'method' => $method,
        ]);
    }

    public function edit($id)
    {
        $method = $this->service->find($id);

        return Inertia::render('Admin/PaymentMethods/Edit', [
            'method' => $method,
        ]);
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name'    => 'required|string|max:255',
            'type'    => ['required', 'string', 'max:255', new UniquePaymentMethodType($id)],
            'mode'    => 'required|in:live,test',
            'details' => 'required|array',
            'status'  => 'required|in:active,deactive',
        ]);

        $this->service->update($id, $validated);

        return redirect()->route('siteadmin.payment-methods.index')->with('success', 'Payment method updated successfully.');
    }

    public function destroy($id)
    {
        $this->service->delete($id);

        return redirect()->route('siteadmin.payment-methods.index')->with('success', 'Payment method deleted successfully.');
    }

    public function trashed()
    {
        $trashed = $this->service->trashed();

        return Inertia::render('Admin/PaymentMethods/Trashed', [
            'methods' => $trashed,
        ]);
    }

    public function restore($id)
    {
        $this->service->restore($id);

        return redirect()->route('siteadmin.payment-methods.trashed')->with('success', 'Payment method restored successfully.');
    }
}
