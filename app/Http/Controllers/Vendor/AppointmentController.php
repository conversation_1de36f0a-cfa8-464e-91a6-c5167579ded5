<?php

declare(strict_types=1);

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\AppointmentService;
use App\Models\PlanServiceUsage;
use App\Models\Seat;
use App\Models\Service;
use App\Models\User;
use App\Services\UserDataService;
use App\Services\VendorAppointmentService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

final class AppointmentController extends Controller
{
    public function __construct(
        private readonly UserDataService $userDataService,
        private readonly VendorAppointmentService $appointmentService
    ) {}

    public function index(Request $request): Response
    {
        $branchId     = Auth::user()->current_branch_id;
        $filters      = $request->only(['search', 'status', 'date', 'sort', 'direction']);
        $appointments = $this->appointmentService->getAppointments($branchId, $filters);
        // Map the appointments collection to include staff_json
        if ($appointments instanceof LengthAwarePaginator) {
            $appointments->setCollection(
                $appointments->getCollection()->map(function ($appointment) {
                    // Calculate total estimated end time and duration for all services
                    $latestEstimatedEndTime = null;
                    $totalDuration          = 0;
                    $earliestStartTime      = null;

                    if ($appointment->status === 'in_progress') {
                        $appointmentServices = $appointment->appointmentServices()
                            ->whereNotNull('estimated_end_time')
                            ->get();

                        if ($appointmentServices->isNotEmpty()) {
                            $latestEstimatedEndTime = $appointmentServices->max('estimated_end_time');
                            $earliestStartTime      = $appointmentServices->min('start_time');

                            // Calculate total duration in minutes
                            if ($earliestStartTime && $latestEstimatedEndTime) {
                                $start         = \Carbon\Carbon::parse($earliestStartTime);
                                $end           = \Carbon\Carbon::parse($latestEstimatedEndTime);
                                $totalDuration = $end->diffInMinutes($start);
                            }
                        }
                    }

                    $getplanService   = [];
                    $getServiceDetail = [];
                    foreach ($appointment->appointmentServices as $servcedetails) {
                        if ($servcedetails->plan_used_service) {
                            $getplanService[] = $servcedetails->plan_used_service;
                        }
                        $getServiceDetail[] = [
                            'appointment_service'                   => $servcedetails->service_name,
                            'appointment_service_price'             => $servcedetails->price,
                            'appointment_service_notes'             => $servcedetails->service_notes,
                            'appointment_service_plan_used_service' => $servcedetails->plan_used_service,
                            'appointment_service_service_id'        => $servcedetails->service_id,
                        ];

                    }

                    $planUsages = PlanServiceUsage::whereIn('plan_service_usages.id', $getplanService)
                        ->join('plan_usages', 'plan_usages.id', '=', 'plan_service_usages.plan_usage_id')
                        ->join('plans', 'plan_usages.plan_id', '=', 'plans.id')
                        ->select('plans.name', 'service_name', 'service_id')->get()->toArray();

                    return [
                        'id'   => $appointment->id,
                        'user' => [
                            'id'    => $appointment->user->id,
                            'name'  => $appointment->user->name,
                            'email' => $appointment->user->email,
                            'phone' => $appointment->user->phone,
                        ],
                        'appointment_date'    => $appointment->appointment_date->format('Y-m-d'),
                        'appointment_time'    => $appointment->appointment_time,
                        'currency_symbol'     => $appointment->currency_symbol,
                        'currency_text'       => $appointment->currency_text,
                        'ticket_number'       => $appointment->ticket_number,
                        'status'              => $appointment->status,
                        'app_price'           => $appointment->appointmentServices->sum('price'),
                        'appoinmentUsedPlan'  => $planUsages ?? [],
                        'appointmentServices' => $getServiceDetail,
                        'services'            => $appointment->services->map(function ($service) use ($appointment) {
                            $appointmentService = $appointment->appointmentServices()
                                ->where('service_id', $service->id)
                                ->first();

                            $seat  = $appointmentService?->seat;
                            $staff = $seat?->staff;

                            return [
                                'id'                 => $service->id,
                                'name'               => $service->name,
                                'duration_minutes'   => $service->duration_minutes,
                                'price'              => $service->price,
                                'estimated_end_time' => $appointmentService?->estimated_end_time,
                                'start_time'         => $appointmentService?->start_time,
                                'seat_id'            => $appointmentService?->seat_id,
                                'seat_name'          => $seat?->name,
                                'staff_name'         => $staff?->name,
                            ];
                        }),
                        'staff_json'               => $appointment->staff_json,
                        'total_estimated_end_time' => $latestEstimatedEndTime,
                        'total_duration_minutes'   => $totalDuration,
                        'earliest_start_time'      => $earliestStartTime,
                        'notes'                    => $appointment->notes,
                    ];
                })
            );
        }

        return Inertia::render('Vendor/Appointment/Appointment', [
            'appointments' => $appointments,
            'filters'      => $filters,
        ]);
    }

    public function create(): Response
    {
        $branchId = Auth::user()->current_branch_id;

        // Get all active services for the current branch
        $services = Service::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get()
            ->map(function ($service) {
                return [
                    'id'               => $service->id,
                    'name'             => $service->name,
                    'duration_minutes' => $service->duration_minutes,
                    'price'            => $service->price,
                ];
            });

        $allUsers = $this->userDataService->getVendorAttachedCustomerIds($branchId);

        // Get all customers (users with customer role)
        $customers = User::whereIn('id', $allUsers)
            ->orderBy('name')
            ->get()
            ->map(function ($customer) {
                return [
                    'id'    => $customer->id,
                    'name'  => $customer->name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                ];
            });

        $allSeats = Seat::where('branch_id', $branchId)->where('status', 'available')->get();

        // Get all staff for this branch
        $staff = User::role('staff')->where('branch_id', $branchId)->get(['id', 'name', 'email', 'phone']);

        return Inertia::render('Vendor/Appointment/AddAppointment', [
            'services'  => $services,
            'customers' => $customers,
            'allSeats'  => $allSeats,
            'staff'     => $staff,
        ]);
    }

    public function store(Request $request)
    {
        $branchId = Auth::user()->current_branch_id;

        $validated = $request->validate([
            'user_id'          => 'required|exists:users,id',
            'appointment_date' => 'required|date|after_or_equal:today',
            'appointment_time' => 'required|date_format:h:i A',
            'services'         => 'required|array|min:1',
            'services.*.id'    => 'required|exists:services,id',
            'seat_id'          => 'nullable|exists:seats,id',
            'notes'            => 'nullable|string|max:500',
            'staff_id'         => 'nullable|exists:users,id',
        ]);

        // Prepare staff_json if staff_id is present
        $staffJson = null;
        $staff_id  = null;
        if (! empty($validated['staff_id'])) {
            $staffUser = User::find($validated['staff_id']);
            if ($staffUser) {
                $staffJson = json_encode([
                    'id'    => $staffUser->id,
                    'name'  => $staffUser->name,
                    'email' => $staffUser->email,
                    'phone' => $staffUser->phone,
                ]);
                $staff_id = $staffUser->id;
            }
        }
        $validated['staff_id']   = $staff_id;
        $validated['staff_json'] = $staffJson;

        $validated['appointment_time'] = date('H:i', strtotime($validated['appointment_time']));

        $this->appointmentService->createAppointment($branchId, $validated);

        return redirect()
            ->route('vendor.appointments.index')
            ->with('success', 'Appointment created successfully');
    }

    public function edit(Appointment $appointment): Response
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure appointment belongs to current branch
        if ($appointment->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $disallow_status = ['completed', 'cancelled', 'in_progress'];

        // Check if appointment status is disallowed
        if (in_array($appointment->status, $disallow_status)) {
            $status = str_replace('_', ' ', $appointment->status); // Convert in_progress to in progress
            $status = ucwords($status); // Capitalize each word -> In Progress
            abort(403, 'You cannot edit this appointment as its status is '.$status.'.');
        }

        // Get all active services for the current branch
        $services = Service::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get()
            ->map(function ($service) {
                return [
                    'id'               => $service->id,
                    'name'             => $service->name,
                    'duration_minutes' => $service->duration_minutes,
                    'price'            => $service->price,
                ];
            });

        $allUsers = $this->userDataService->getVendorAttachedCustomerIds($branchId);

        // Get all customers (users with customer role)
        $customers = User::whereIn('id', $allUsers)
            ->orderBy('name')
            ->get()
            ->map(function ($customer) {
                return [
                    'id'    => $customer->id,
                    'name'  => $customer->name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                ];
            });

        // Format appointment data with service details from appointment_services
        $appointmentData = [
            'id'               => $appointment->id,
            'user_id'          => $appointment->user_id,
            'appointment_date' => $appointment->appointment_date->format('Y-m-d'),
            'appointment_time' => date('h:i A', strtotime($appointment->appointment_time)),
            'currency_symbol'  => $appointment->currency_symbol,
            'currency_text'    => $appointment->currency_text,
            'ticket_number'    => $appointment->ticket_number,
            'status'           => $appointment->status,
            'notes'            => $appointment->notes,
            'services'         => $appointment->appointmentServices->map(function ($appointmentService) {
                return [
                    'id'               => $appointmentService->service->id,
                    'name'             => $appointmentService->service_name,
                    'duration_minutes' => $appointmentService->service->duration_minutes,
                    'price'            => $appointmentService->price,
                    'seat_id'          => $appointmentService->seat_id,
                ];
            }),
            'staff_id' => $appointment->staff_id,
        ];

        $allSeats = Seat::where('branch_id', $branchId)->where('status', 'available')->get();

        // Get all staff for this branch
        $staff = User::role('staff')->where('branch_id', $branchId)->get(['id', 'name', 'email', 'phone']);

        return Inertia::render('Vendor/Appointment/EditAppointment', [
            'appointment' => $appointmentData,
            'services'    => $services,
            'customers'   => $customers,
            'allSeats'    => $allSeats,
            'staff'       => $staff,
        ]);
    }

    public function update(Request $request, Appointment $appointment)
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure appointment belongs to current branch
        if ($appointment->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $disallow_status = ['completed', 'cancelled', 'in_progress'];

        // Check if appointment status is disallowed
        if (in_array($appointment->status, $disallow_status)) {
            $status = str_replace('_', ' ', $appointment->status); // Convert in_progress to in progress
            $status = ucwords($status); // Capitalize each word -> In Progress
            abort(403, 'You cannot edit this appointment as its status is '.$status.'.');
        }

        $validated = $request->validate([
            'user_id'          => 'required|exists:users,id',
            'appointment_date' => 'required|date',
            'appointment_time' => 'required|date_format:h:i A',
            'services'         => 'required|array|min:1',
            'services.*.id'    => 'required|exists:services,id',
            'seat_id'          => 'required_unless:status,pending|nullable|exists:seats,id',
            'notes'            => 'nullable|string|max:500',
            'status'           => 'required|in:pending,in_progress,completed,cancelled',
            'staff_id'         => 'nullable|exists:users,id',
        ]);

        // Prepare staff_json if staff_id is present
        $staffJson = null;
        $staff_id  = null;
        if (! empty($validated['staff_id'])) {
            $staffUser = User::find($validated['staff_id']);
            if ($staffUser) {
                $staffJson = json_encode([
                    'id'    => $staffUser->id,
                    'name'  => $staffUser->name,
                    'email' => $staffUser->email,
                    'phone' => $staffUser->phone,
                ]);
                $staff_id = $staffUser->id;
            }
        }
        $validated['staff_id']   = $staff_id;
        $validated['staff_json'] = $staffJson;

        // Check if seat is already assigned to any service in  in_progress state
        if ($validated['status'] === 'in_progress' && ! empty($validated['seat_id'])) {
            $conflictingServiceExists = AppointmentService::where('seat_id', $validated['seat_id'])
                ->whereHas('appointment', function ($query) use ($branchId, $appointment) {
                    $query->where('status', 'in_progress')
                        ->where('branch_id', $branchId)
                        ->where('id', '<>', $appointment->id);
                })
                ->exists();

            if ($conflictingServiceExists) {
                return back()->withErrors([
                    'seat_id' => 'Cannot mark appointment as In Progress because services on this seat are still in progress.',
                ])->withInput();
            }
        }

        $validated['appointment_time'] = date('H:i', strtotime($validated['appointment_time']));
        $this->appointmentService->updateAppointment($appointment, $branchId, $validated);

        return redirect()
            ->route('vendor.appointments.index')
            ->with('success', 'Appointment updated successfully');
    }

    public function destroy(Appointment $appointment)
    {
        $branchId = Auth::user()->current_branch_id;
        $appointment->where('branch_id', $branchId)->firstOrFail();

        $disallow_status = ['completed', 'cancelled', 'in_progress'];

        // Check if appointment status is disallowed
        if (in_array($appointment->status, $disallow_status)) {
            $status = str_replace('_', ' ', $appointment->status); // Convert in_progress to in progress
            $status = ucwords($status); // Capitalize each word -> In Progress

            return redirect()->back()->with('error', 'You cannot delete this appointment as its status is '.$status.'.');

        }

        // Check if appointment can be deleted
        if ($appointment->status !== 'pending' && $appointment->status !== 'cancelled') {
            return redirect()
                ->route('vendor.appointments.index')
                ->with('error', 'Only pending or cancelled appointments can be deleted');
        }

        $this->appointmentService->deleteAppointment($appointment, $branchId);

        return redirect()
            ->route('vendor.appointments.index')
            ->with('success', 'Appointment deleted successfully');
    }

    public function updateStatus(Request $request, Appointment $appointment)
    {
        $branchId = Auth::user()->current_branch_id;
        $appointment->where('branch_id', $branchId)->firstOrFail();

        $validated = $request->validate([
            'status'     => 'required|in:pending,in_progress,completed,cancelled',
            'service_id' => 'required|exists:appointment_services,service_id,appointment_id,'.$appointment->id,
        ]);

        $this->appointmentService->updateServiceStatus($appointment, $validated['service_id'], $validated['status']);

        // Check if all services are completed
        $allServicesCompleted = $appointment->appointmentServices()
            ->where('status', '!=', 'completed')
            ->count() === 0;

        // If all services are completed, update the main appointment status
        if ($allServicesCompleted) {
            $appointment->update([
                'status' => 'completed',
            ]);
        }

        return redirect()
            ->back()
            ->with('success', 'Service status updated successfully');
    }

    public function calendar(): Response
    {
        return Inertia::render('Vendor/Appointment/CalendarView');
    }

    public function getCalendarData(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $branchId = Auth::user()->current_branch_id;
            $start    = $request->input('start')
                ? Carbon::parse($request->input('start'))->startOfDay()
                : now()->startOfDay();
            $end = $request->input('end')
                ? Carbon::parse($request->input('end'))->endOfDay()
                : now()->addMonths(3)->endOfDay();

            if ($end->lt($start)) {
                return response()->json([
                    'success' => false,
                    'message' => 'End date cannot be before start date',
                ], 400);
            }

            $maxEndDate = now()->addYear();
            if ($end->gt($maxEndDate)) {
                $end = $maxEndDate;
            }

            $appointments = $this->appointmentService->getCalendarData($branchId, $start, $end)
                ->map(function ($appointment) {
                    $statusColors = [
                        'pending'     => '#FFA500',
                        'in_progress' => '#1E90FF',
                        'completed'   => '#32CD32',
                        'cancelled'   => '#FF0000',
                    ];
                    $totalDuration   = $appointment->services->sum('duration_minutes');
                    $appointmentDate = Carbon::parse($appointment->appointment_date)->format('Y-m-d');
                    $appointmentTime = Carbon::parse($appointment->appointment_time)->format('H:i:s');
                    $startTime       = Carbon::parse($appointmentDate.' '.$appointmentTime);
                    $endTime         = $startTime->copy()->addMinutes($totalDuration);

                    return [
                        'id'              => $appointment->id,
                        'title'           => $appointment->user->name,
                        'start'           => $startTime->format('Y-m-d\TH:i:s'),
                        'end'             => $endTime->format('Y-m-d\TH:i:s'),
                        'backgroundColor' => $statusColors[$appointment->status] ?? '#808080',
                        'borderColor'     => $statusColors[$appointment->status] ?? '#808080',
                        'extendedProps'   => [
                            'ticket_number'  => $appointment->ticket_number,
                            'status'         => $appointment->status,
                            'services'       => $appointment->services->pluck('name'),
                            'notes'          => $appointment->notes,
                            'customer_phone' => $appointment->user->phone,
                            'customer_email' => $appointment->user->email,
                        ],
                    ];
                });

            return response()->json([
                'success' => true,
                'data'    => $appointments,
            ]);
        } catch (\Exception $e) {
            Log::error('Calendar data error: '.$e->getMessage(), [
                'exception' => $e,
                'request'   => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unable to load appointments. Please try again later.',
            ], 500);
        }
    }

    public function seatMap(): Response
    {
        $branchId = Auth::user()->current_branch_id;

        $user = Auth::user();

        $seatMapData = $this->appointmentService->getSeatMapData($branchId, $user->hasRole('staff') ? $user->id : null);

        $seats = $seatMapData['seats']->map(function ($seat) {
            return [
                'id'     => $seat->id,
                'name'   => $seat->name,
                'status' => $seat->status,
                'staff'  => $seat->staff ? [
                    'id'   => $seat->staff->id,
                    'name' => $seat->staff->name,
                ] : null,
                'appointments' => $seat->appointmentServices->map(function ($appointmentService) {
                    return [
                        'id'                 => $appointmentService->appointment->id,
                        'ticket_number'      => $appointmentService->appointment->ticket_number,
                        'status'             => $appointmentService->status,
                        'service_name'       => $appointmentService->service_name,
                        'service_id'         => $appointmentService->service_id,
                        'start_time'         => $appointmentService->start_time,
                        'estimated_end_time' => $appointmentService->estimated_end_time,
                        'user'               => [
                            'id'    => $appointmentService->appointment->user->id,
                            'name'  => $appointmentService->appointment->user->name,
                            'email' => $appointmentService->appointment->user->email,
                            'phone' => $appointmentService->appointment->user->phone,
                        ],
                    ];
                }),
            ];
        });

        $unassignedAppointments = $seatMapData['unassignedAppointments']->map(function ($appointment) {
            return [
                'id'               => $appointment->id,
                'ticket_number'    => $appointment->ticket_number,
                'status'           => $appointment->status,
                'appointment_date' => $appointment->appointment_date->format('Y-m-d'),
                'appointment_time' => $appointment->appointment_time,
                'services'         => $appointment->services->map(function ($service) {
                    return [
                        'id'               => $service->id,
                        'name'             => $service->name,
                        'duration_minutes' => $service->duration_minutes,
                    ];
                }),
                'user' => [
                    'id'    => $appointment->user->id,
                    'name'  => $appointment->user->name,
                    'email' => $appointment->user->email,
                    'phone' => $appointment->user->phone,
                ],
            ];
        });
        $assignedPendingAppointments = $seatMapData['assignedPendingAppointments']->map(function ($appointment) {
            return [
                'id'               => $appointment->id,
                'ticket_number'    => $appointment->ticket_number,
                'status'           => $appointment->status,
                'appointment_date' => $appointment->appointment_date->format('Y-m-d'),
                'appointment_time' => $appointment->appointment_time,
                'services'         => $appointment->appointmentServices->map(function ($service) {
                    return [
                        'id'               => $service->service_id,
                        'name'             => $service->service_name,
                        'duration_minutes' => $service->service->duration_minutes,
                        'status'           => $service->status,
                        'seat_id'          => $service->seat_id,
                        'seat_name'        => $service->seat ? $service->seat->name : null,
                    ];
                }),
                'user' => [
                    'id'    => $appointment->user->id,
                    'name'  => $appointment->user->name,
                    'email' => $appointment->user->email,
                    'phone' => $appointment->user->phone,
                ],
            ];
        });

        return Inertia::render('Vendor/Appointment/SeatMap', [
            'seats'                       => $seats,
            'unassignedAppointments'      => $unassignedAppointments,
            'assignedPendingAppointments' => $assignedPendingAppointments,
        ]);
    }

    public function checkSeatAvailability(Seat $seat): \Illuminate\Http\JsonResponse
    {
        $branchId  = Auth::user()->current_branch_id;
        $available = $this->appointmentService->checkSeatAvailability($seat, $branchId);
        if ($seat->branch_id !== $branchId) {
            return response()->json(['error' => 'Unauthorized action.'], 403);
        }

        return response()->json([
            'available' => $available,
        ]);
    }

    public function updateSeatStatus(Request $request, Appointment $appointment)
    {
        $branchId = Auth::user()->current_branch_id;
        if ($appointment->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }
        $validated = $request->validate([
            'status'  => 'required|in:pending,in_progress,completed,cancelled',
            'seat_id' => 'required_if:status,in_progress|nullable|exists:seats,id',
        ]);
        if ($validated['status'] === 'in_progress') {
            $conflictingServiceExists = AppointmentService::where('seat_id', $validated['seat_id'])
                ->whereIn('status', ['pending', 'in_progress'])
                ->whereHas('appointment', function ($query) use ($branchId) {
                    $query->where('status', 'in_progress')
                        ->where('branch_id', $branchId);
                })
                ->exists();
            if ($conflictingServiceExists) {
                return back()->withErrors([
                    'seat_id' => 'Cannot mark appointment as In Progress because one or more services on this seat are still pending or in progress.',
                ])->withInput();
            }
        }
        $this->appointmentService->updateSeatStatus($appointment, $branchId, $validated['status'], $validated['seat_id'] ?? null);

        return redirect()->back()->with('success', 'Appointment status updated successfully');
    }
}
