<?php

declare(strict_types=1);

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Seat;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

final class SeatController extends Controller
{
    public function index(Request $request): Response
    {
        $branchId = Auth::user()->current_branch_id;

        $query = Seat::with('staff')
            ->where('branch_id', $branchId);

        // Apply search filter
        if ($request->search) {
            $query->where('name', 'like', "%{$request->search}%")
                ->orWhere('notes', 'like', "%{$request->search}%");
        }

        // Apply status filter
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'name';
        $sortDirection = $request->direction ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        $seats = $query->paginate(10)
            ->withQueryString()
            ->through(fn ($seat) => [
                'id'     => $seat->id,
                'name'   => $seat->name,
                'status' => $seat->status,
                'staff'  => $seat->staff ? [
                    'id'   => $seat->staff->id,
                    'name' => $seat->staff->name,
                ] : null,
                'notes' => $seat->notes,
            ]);

        return Inertia::render('Vendor/Seats/Index', [
            'seats'   => $seats,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    public function create(): Response
    {
        $branchId = Auth::user()->current_branch_id;

        // Get all staff members from the current branch with 'staff' role
        $staff = User::role('staff')
            ->where('branch_id', $branchId)
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('Vendor/Seats/Create', [
            'staff' => $staff,
        ]);
    }

    public function store(Request $request)
    {
        $branchId = Auth::user()->current_branch_id;

        $validated = $request->validate([
            'name'     => 'required|string|max:255',
            'staff_id' => 'nullable|exists:users,id',
            // 'status'   => 'required|in:available,occupied,cleaning,maintenance',
            'notes' => 'nullable|string|max:500',
        ]);

        // Handle the "none" value from the frontend
        if ($validated['staff_id'] === 'none') {
            $validated['staff_id'] = null;
        }

        // Custom validation: staff_id can only be assigned to one seat per branch
        if (! empty($validated['staff_id'])) {
            $existingSeat = Seat::where('branch_id', $branchId)
                ->where('staff_id', $validated['staff_id'])
                ->first();
            if ($existingSeat) {
                return back()->withErrors([
                    'staff_id' => 'This staff member is already assigned to seat: '.$existingSeat->name,
                ])->withInput();
            }
        }

        $validated['branch_id'] = $branchId;

        Seat::create($validated);

        return redirect()
            ->route('vendor.seats.index')
            ->with('success', 'Seat created successfully');
    }

    public function edit(Seat $seat): Response
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the seat belongs to the current branch
        if ($seat->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $seat->load('staff');

        // Get all staff members from the current branch with 'staff' role
        $staff = User::role('staff')
            ->where('branch_id', $branchId)
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('Vendor/Seats/Edit', [
            'seat'  => $seat,
            'staff' => $staff,
        ]);
    }

    public function update(Request $request, Seat $seat)
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the seat belongs to the current branch
        if ($seat->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name'     => 'required|string|max:255',
            'staff_id' => 'nullable|exists:users,id',
            // 'status'   => 'required|in:available,occupied,cleaning,maintenance',
            'notes' => 'nullable|string|max:500',
        ]);

        $validated['status'] = 'available';

        // Handle the "none" value from the frontend
        if ($validated['staff_id'] === 'none') {
            $validated['staff_id'] = null;
        }

        // Custom validation: staff_id can only be assigned to one seat per branch (excluding this seat)
        if (! empty($validated['staff_id'])) {
            $existingSeat = Seat::where('branch_id', $branchId)
                ->where('staff_id', $validated['staff_id'])
                ->where('id', '!=', $seat->id)
                ->first();
            if ($existingSeat) {
                return back()->withErrors([
                    'staff_id' => 'This staff member is already assigned to seat: '.$existingSeat->name,
                ])->withInput();
            }
        }

        $seat->update($validated);

        return redirect()
            ->route('vendor.seats.index')
            ->with('success', 'Seat updated successfully');
    }

    public function destroy(Seat $seat)
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the seat belongs to the current branch
        if ($seat->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        // Check if seat has any active appointments
        if ($seat->appointmentServices()->where('status', '!=', 'completed')->count() > 0) {
            return redirect()
                ->route('vendor.seats.index')
                ->with('error', 'Cannot delete a seat that has active appointments');
        }

        $seat->delete();

        return redirect()
            ->route('vendor.seats.index')
            ->with('success', 'Seat deleted successfully');
    }
}
