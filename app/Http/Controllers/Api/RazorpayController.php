<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PaymentMethod;
use App\Models\PlanTransaction;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RazorpayController extends Controller
{
    public function updateTransactionStatus(Request $request)
    {
        try {
            $validated = $request->validate([
                'transaction_id' => 'required|exists:plan_transactions,id',
                'status'         => 'required|in:cancelled,failed',
                'reason'         => 'nullable|string',
            ]);

            $transaction = PlanTransaction::findOrFail($validated['transaction_id']);

            // Verify user owns this transaction
            if (Auth::id() !== $transaction->user_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized',
                ], 403);
            }

            // Update transaction status
            $transaction->update([
                'status'         => $validated['status'],
                'payment_status' => $validated['status'] === 'cancelled' ? 'cancelled' : 'failed',
                'failed_at'      => now(),
                'failure_reason' => $validated['reason'] ?? 'User cancelled the payment',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Transaction status updated',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function createOrder(Request $request)
    {
        try {
            $validated = $request->validate([
                'amount'   => 'required|numeric|min:1',
                'currency' => 'required|string|size:3',
                'plan_id'  => 'required|exists:subscription_plans,id',
            ]);

            $plan           = SubscriptionPlan::findOrFail($validated['plan_id']);
            $razorpayMethod = PaymentMethod::where('type', 'razorpay')
                ->where('status', 'active')
                ->first();

            if (! $razorpayMethod) {
                return response()->json([
                    'success' => false,
                    'message' => 'Razorpay payment method not configured',
                ], 400);
            }

            $keyId     = $razorpayMethod->details['key_id'];
            $keySecret = $razorpayMethod->details['key_secret'];

            // Create Razorpay order
            $orderData = [
                'receipt'  => 'subscription_'.$plan->id.'_'.time(),
                'amount'   => $validated['amount'], // Amount in paise
                'currency' => $validated['currency'],
                'notes'    => [
                    'plan_id'   => $plan->id,
                    'plan_name' => $plan->name,
                ],
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://api.razorpay.com/v1/orders');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_USERPWD, $keyId.':'.$keySecret);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create Razorpay order',
                ], 400);
            }

            $order = json_decode($response, true);

            // Create transaction record with pending status
            $user = Auth::user();
            if ($user) {
                $transaction = PlanTransaction::create([
                    'user_id'              => $user->id,
                    'subscription_plan_id' => $plan->id,
                    'payment_method_id'    => $razorpayMethod->id,
                    'razorpay_order_id'    => $order['id'],
                    'razorpay_order_data'  => $order,
                    'amount'               => $order['amount'] / 100, // Convert from paise to rupees
                    'currency'             => $order['currency'],
                    'status'               => 'pending',
                    'payment_status'       => 'created',
                ]);
            }

            return response()->json([
                'success'        => true,
                'order'          => $order,
                'transaction_id' => $transaction->id ?? null,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
