<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use App\Models\PlanTransaction;
use App\Models\SubscriptionPlan;
use App\Models\User;
use App\Models\VendorSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class SubscriptionCheckoutController extends Controller
{
    public function checkout(Request $request, $planId)
    {
        $plan = SubscriptionPlan::active()->findOrFail($planId);

        // Redirect to login if user is not authenticated
        if (! Auth::check()) {
            return redirect()->route('login')->with('info', 'Please login to continue with your subscription.');
        }

        $authUser = Auth::user();

        // Redirect to login if user doesn't have vendor role
        if (! $authUser->hasRole('vendor')) {
            return redirect()->route('login')->with('info', 'Please login with a vendor account to purchase subscriptions.');
        }

        // Check if user already has an active subscription
        $existingActiveSubscription = VendorSubscription::where('user_id', $authUser->id)
            ->where('status', 'active')
            ->where('ends_at', '>', now())
            ->first();

        // Get active payment methods
        $paymentMethods = PaymentMethod::where('status', 'active')
            ->get()
            ->map(function ($method) {
                return [
                    'id'      => $method->id,
                    'name'    => $method->name,
                    'type'    => $method->type,
                    'mode'    => $method->mode,
                    'details' => $method->details,
                ];
            });

        return Inertia::render('Pages/SubscriptionCheckout', [
            'layout' => 'blank',
            'plan'   => [
                'id'                         => $plan->id,
                'name'                       => $plan->name,
                'description'                => $plan->description,
                'price'                      => $plan->price,
                'currency_symbol'            => $plan->currency_symbol,
                'currency_code'              => $plan->currency_code,
                'billing_cycle'              => $plan->billing_cycle,
                'max_services'               => $plan->max_services,
                'max_appointments_per_month' => $plan->max_appointments_per_month,
                'max_seats'                  => $plan->max_seats,
                'max_branches'               => $plan->max_branches,
                'max_staff'                  => $plan->max_staff,
                'has_analytics'              => $plan->has_analytics,
                'has_api_access'             => $plan->has_api_access,
                'has_custom_branding'        => $plan->has_custom_branding,
                'has_priority_support'       => $plan->has_priority_support,
            ],
            'paymentMethods' => $paymentMethods,
            'authUser'       => $authUser ? [
                'id'    => $authUser->id,
                'name'  => $authUser->name,
                'email' => $authUser->email,
                'phone' => $authUser->phone,
            ] : null,
            'hasActiveSubscription' => $existingActiveSubscription !== null,
            'activeSubscription'    => $existingActiveSubscription ? [
                'id'             => $existingActiveSubscription->id,
                'plan_name'      => $existingActiveSubscription->subscriptionPlan->name,
                'ends_at'        => $existingActiveSubscription->ends_at->format('M d, Y'),
                'days_remaining' => $existingActiveSubscription->daysUntilExpiry(),
            ] : null,
        ]);
    }

    public function processPayment(Request $request)
    {
        // Ensure user is authenticated
        if (! Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to continue.');
        }

        $validated = $request->validate([
            'plan_id'           => 'required|exists:subscription_plans,id',
            'payment_method_id' => 'required|exists:payment_methods,id',
            'payment_id'        => 'required|string', // Razorpay payment ID
            'order_id'          => 'required|string', // Razorpay order ID
            'signature'         => 'required|string', // Razorpay signature
            'transaction_id'    => 'nullable|exists:plan_transactions,id', // Transaction ID from order creation
        ]);

        try {
            DB::beginTransaction();

            $user          = Auth::user();
            $plan          = SubscriptionPlan::findOrFail($validated['plan_id']);
            $paymentMethod = PaymentMethod::findOrFail($validated['payment_method_id']);

            // Verify user has vendor role
            if (! $user->hasRole('vendor')) {
                throw new \Exception('Only vendor users can purchase subscriptions');
            }

            // Check if user already has an active subscription
            $existingActiveSubscription = VendorSubscription::where('user_id', $user->id)
                ->where('status', 'active')
                ->where('ends_at', '>', now())
                ->first();

            if ($existingActiveSubscription) {
                throw new \Exception('You already have an active subscription. Please wait for it to expire before purchasing a new plan.');
            }

            // Verify Razorpay signature for security
            if (! $this->verifyRazorpaySignature([
                'order_id'   => $validated['order_id'],
                'payment_id' => $validated['payment_id'],
                'signature'  => $validated['signature'],
            ], $paymentMethod)) {
                throw new \Exception('Invalid payment signature');
            }

            // Find existing transaction or create new one
            if (! empty($validated['transaction_id'])) {
                // Update existing transaction
                $transaction = PlanTransaction::findOrFail($validated['transaction_id']);

                // Make sure this transaction belongs to the current user
                if ($transaction->user_id !== $user->id) {
                    throw new \Exception('Invalid transaction');
                }

                // Fetch payment details from Razorpay
                $paymentData = $this->fetchRazorpayPaymentDetails($validated['payment_id'], $paymentMethod);

                // Update transaction with payment details
                $transaction->update([
                    'razorpay_payment_id'   => $validated['payment_id'],
                    'razorpay_signature'    => $validated['signature'],
                    'razorpay_payment_data' => $paymentData,
                    'status'                => 'completed',
                    'payment_status'        => 'captured',
                    'paid_at'               => now(),
                    'notes'                 => 'Payment completed successfully via '.$paymentMethod->name,
                ]);
            } else {
                // Fallback: Create new transaction record if transaction_id is missing
                // This should rarely happen as we always create a transaction during order creation

                // Fetch payment details from Razorpay
                $paymentData = $this->fetchRazorpayPaymentDetails($validated['payment_id'], $paymentMethod);

                $transaction = PlanTransaction::create([
                    'user_id'               => $user->id,
                    'subscription_plan_id'  => $plan->id,
                    'payment_method_id'     => $paymentMethod->id,
                    'razorpay_order_id'     => $validated['order_id'],
                    'razorpay_payment_id'   => $validated['payment_id'],
                    'razorpay_signature'    => $validated['signature'],
                    'razorpay_payment_data' => $paymentData,
                    'amount'                => $plan->price,
                    'currency'              => $plan->currency_code,
                    'status'                => 'completed',
                    'payment_status'        => 'captured',
                    'paid_at'               => now(),
                    'notes'                 => 'Payment completed successfully via '.$paymentMethod->name,
                ]);
            }

            // Create new vendor subscription with transaction reference
            // Note: We've already checked that user doesn't have an active subscription above
            $subscription = VendorSubscription::create([
                'user_id'              => $user->id,
                'subscription_plan_id' => $plan->id,
                'plan_transaction_id'  => $transaction->id,
                'starts_at'            => now(),
                'ends_at'              => $plan->billing_cycle === 'monthly' ? now()->addMonth() : now()->addYear(),
                'status'               => 'active',
                'billing_period_start' => now()->startOfMonth(),
                'billing_period_end'   => now()->endOfMonth(),
            ]);

            // Update usage counts for the new subscription
            $subscription->updateUsageCounts();

            DB::commit();

            return redirect()->route('vendor-home')->with('success', 'Subscription activated successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            // If we have a transaction ID, mark it as failed
            if (! empty($validated['transaction_id'])) {
                try {
                    $transaction = PlanTransaction::find($validated['transaction_id']);
                    if ($transaction && $transaction->user_id === Auth::id()) {
                        $transaction->update([
                            'status'         => 'failed',
                            'payment_status' => 'failed',
                            'failed_at'      => now(),
                            'failure_reason' => $e->getMessage(),
                        ]);
                    }
                } catch (\Exception $updateException) {
                    // Log the error but don't fail the main response
                    \Log::error('Failed to update transaction status: '.$updateException->getMessage());
                }
            }

            return back()->withErrors(['payment' => 'Payment processing failed: '.$e->getMessage()]);
        }
    }

    private function verifyRazorpaySignature($data, $paymentMethod)
    {
        $keySecret         = $paymentMethod->details['key_secret'];
        $expectedSignature = hash_hmac('sha256', $data['order_id'].'|'.$data['payment_id'], $keySecret);

        return hash_equals($expectedSignature, $data['signature']);
    }

    /**
     * Fetch payment details from Razorpay API
     */
    private function fetchRazorpayPaymentDetails($paymentId, $paymentMethod)
    {
        try {
            $keyId     = $paymentMethod->details['key_id'];
            $keySecret = $paymentMethod->details['key_secret'];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://api.razorpay.com/v1/payments/{$paymentId}");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_USERPWD, $keyId.':'.$keySecret);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                return json_decode($response, true);
            }

            return null;
        } catch (\Exception $e) {
            \Log::error('Failed to fetch Razorpay payment details: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Create a new Razorpay order
     */
    public function createRazorpayOrder(Request $request)
    {
        try {
            $validated = $request->validate([
                'amount'   => 'required|numeric|min:1',
                'currency' => 'required|string|size:3',
                'plan_id'  => 'required|exists:subscription_plans,id',
            ]);

            // Ensure user is authenticated
            if (! Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }

            $plan           = SubscriptionPlan::findOrFail($validated['plan_id']);
            $razorpayMethod = PaymentMethod::where('type', 'razorpay')
                ->where('status', 'active')
                ->first();

            if (! $razorpayMethod) {
                return response()->json([
                    'success' => false,
                    'message' => 'Razorpay payment method not configured',
                ], 400);
            }

            $keyId     = $razorpayMethod->details['key_id'];
            $keySecret = $razorpayMethod->details['key_secret'];

            // Create Razorpay order
            $orderData = [
                'receipt'  => 'subscription_'.$plan->id.'_'.time(),
                'amount'   => $validated['amount'], // Amount in paise
                'currency' => $validated['currency'],
                'notes'    => [
                    'plan_id'   => $plan->id,
                    'plan_name' => $plan->name,
                ],
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://api.razorpay.com/v1/orders');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_USERPWD, $keyId.':'.$keySecret);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create Razorpay order',
                ], 400);
            }

            $order = json_decode($response, true);

            // Create transaction record with pending status
            $user        = Auth::user();
            $transaction = PlanTransaction::create([
                'user_id'              => $user->id,
                'subscription_plan_id' => $plan->id,
                'payment_method_id'    => $razorpayMethod->id,
                'razorpay_order_id'    => $order['id'],
                'razorpay_order_data'  => $order,
                'amount'               => $order['amount'] / 100, // Convert from paise to rupees
                'currency'             => $order['currency'],
                'status'               => 'pending',
                'payment_status'       => 'created',
            ]);

            return response()->json([
                'success'        => true,
                'order'          => $order,
                'transaction_id' => $transaction->id,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update transaction status (for cancelled or failed payments)
     */
    public function updateTransactionStatus(Request $request)
    {
        try {
            $validated = $request->validate([
                'transaction_id' => 'required|exists:plan_transactions,id',
                'status'         => 'required|in:cancelled,failed',
                'reason'         => 'nullable|string',
            ]);

            // Ensure user is authenticated
            if (! Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }

            $transaction = PlanTransaction::findOrFail($validated['transaction_id']);

            // Verify user owns this transaction
            if (Auth::id() !== $transaction->user_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized',
                ], 403);
            }

            // Update transaction status
            $transaction->update([
                'status'         => $validated['status'],
                'payment_status' => $validated['status'] === 'cancelled' ? 'cancelled' : 'failed',
                'failed_at'      => now(),
                'failure_reason' => $validated['reason'] ?? 'User cancelled the payment',
            ]);

            return response()->json([
                'success'      => true,
                'message'      => 'Transaction status updated',
                'redirect_url' => route('vendor-home'),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
