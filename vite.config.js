import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import laravel from 'laravel-vite-plugin';
import { visualizer } from 'rollup-plugin-visualizer'; // Optional but helpful

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/js/src/main.tsx'],
            refresh: true,
            ssr: 'resources/js/ssr.tsx',
        }),
        react(),
        visualizer({ open: true }) // Remove if you don't want visual analysis
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './resources/js/src'),
            '@components': path.resolve(__dirname, './resources/js/src/components'),
            '@layouts': path.resolve(__dirname, './resources/js/src/layouts'),
            '@pages': path.resolve(__dirname, './resources/js/src/pages'),
            '@store': path.resolve(__dirname, './resources/js/src/store'),
            '@hooks': path.resolve(__dirname, './resources/js/src/hooks'),
            '@utils': path.resolve(__dirname, './resources/js/src/utils'),
            '@assets': path.resolve(__dirname, './resources/js/src/assets'),
            '@css': path.resolve(__dirname, './resources/js/src/assets/css'),
            '@images': path.resolve(__dirname, './resources/js/src/assets/images'),
        },
    },
    build: {
        chunkSizeWarningLimit: 5000, // Optional: raise limit to suppress warnings
        manifest: true, // Enable manifest generation
        rollupOptions: {
            output: {
                manualChunks(id) {
                    if (id.includes('node_modules')) {
                        if (id.includes('axios')) return 'axios';
                        if (id.includes('lodash')) return 'lodash';
                        if (id.includes('apexcharts') || id.includes('react-apexcharts')) return 'apexcharts'; // NEW
                        return 'vendor';
                    }
                }
            },
        },
    },
});
