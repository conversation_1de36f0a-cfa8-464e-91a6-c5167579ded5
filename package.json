{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build && vite build --ssr && npm run fix-build-paths", "fix-build-paths": "node -e \"const fs = require('fs'); const path = require('path'); if (fs.existsSync('public/build/.vite/manifest.json')) { fs.renameSync('public/build/.vite/manifest.json', 'public/build/manifest.json'); fs.rmSync('public/build/.vite', { recursive: true, force: true }); } if (fs.existsSync('bootstrap/ssr/ssr-manifest.json')) { fs.renameSync('bootstrap/ssr/ssr-manifest.json', 'public/build/ssr-manifest.json'); } if (fs.existsSync('bootstrap/ssr/ssr.js')) { fs.renameSync('bootstrap/ssr/ssr.js', 'public/build/ssr.js'); }\""}, "dependencies": {"@fullcalendar/core": "^6.0.0", "@fullcalendar/daygrid": "^6.0.0", "@fullcalendar/interaction": "^6.0.0", "@fullcalendar/react": "^6.0.0", "@fullcalendar/timegrid": "^6.0.0", "@inertiajs/react": "^2.0.11", "@mantine/core": "^5.10.5", "@mantine/hooks": "^5.10.5", "@popperjs/core": "^2.11.6", "@reduxjs/toolkit": "^2.2.7", "@types/lodash": "^4.17.17", "@types/node": "^22.4.0", "@types/react": "^18.0.21", "@types/react-dom": "^18.0.6", "@types/react-text-mask": "^5.4.11", "apexcharts": "^3.37.1", "easymde": "^2.18.0", "formik": "^2.2.9", "highlight.js": "^11.6.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-18-image-lightbox": "^5.1.4", "react-apexcharts": "^1.4.0", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.3.2", "react-dom": "^18.2.0", "react-flatpickr": "^3.10.13", "react-icons": "^5.5.0", "react-images-uploading": "^3.1.7", "react-popper": "^2.3.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.4.2", "react-select": "^5.5.8", "react-simplemde-editor": "^5.2.0", "react-text-mask": "^5.5.0", "sweetalert2": "^11.6.8", "sweetalert2-react-content": "^5.0.7", "swiper": "^11.1.9", "yup": "^1.4.0"}, "devDependencies": {"@headlessui/react": "^2.1.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "@types/react": "^18.0.27", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^18.0.10", "@types/react-flatpickr": "^3.8.8", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "depcheck": "^1.4.7", "i18next": "^23.13.0", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "laravel-vite-plugin": "^1.0.5", "react-animate-height": "^3.0.4", "react-i18next": "^15.0.1", "react-perfect-scrollbar": "^1.5.8", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.1", "typescript": "^4.8.4", "vite": "^7.0.4"}}