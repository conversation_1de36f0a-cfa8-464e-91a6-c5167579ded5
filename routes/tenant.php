<?php

declare(strict_types=1);

use App\Http\Controllers\User\UserDashboardController;
use App\Http\Controllers\Vendor\AppointmentController;
use App\Http\Controllers\Vendor\BranchController;
use App\Http\Controllers\Vendor\BranchMediaController;
use App\Http\Controllers\Vendor\CustomerController;
use App\Http\Controllers\Vendor\DashboardController;
use App\Http\Controllers\Vendor\PlanController;
use App\Http\Controllers\Vendor\SeatController;
use App\Http\Controllers\Vendor\ServiceController;
use App\Http\Controllers\Vendor\StaffController;
use App\Http\Controllers\Vendor\TermConditionController;
use App\Http\Controllers\Vendor\VendorFrontController;
use App\Http\Controllers\Vendor\WorkingHourController;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    Route::middleware(['auth', 'tenant', 'role:vendor|branchuser'])->group(function () {

        Route::prefix('vendor')->name('vendor.')->group(function () {
            Route::get('/dashboard', [DashboardController::class, 'index'])->name('home');
            // Appointment Management Routes
            Route::prefix('appointments')->name('appointments.')->group(function () {
                Route::get('/', [AppointmentController::class, 'index'])->name('index');
                Route::get('/create', [AppointmentController::class, 'create'])->name('create');
                Route::post('/', [AppointmentController::class, 'store'])->name('store');
                Route::get('/{appointment}/edit', [AppointmentController::class, 'edit'])->name('edit');
                Route::put('/{appointment}', [AppointmentController::class, 'update'])->name('update');
                Route::delete('/{appointment}', [AppointmentController::class, 'destroy'])->name('destroy');
                Route::put('/{appointment}/update-status', [AppointmentController::class, 'updateStatus'])->name('update-status');
                Route::put('/{appointment}/update-seat-status', [AppointmentController::class, 'updateSeatStatus'])->name('update-seat-status');
                Route::get('/calendar', [AppointmentController::class, 'calendar'])->name('calendar');
                Route::get('/calendar-data', [AppointmentController::class, 'getCalendarData'])->name('calendar-data');
                Route::get('/seat-map', [AppointmentController::class, 'seatMap'])->name('seat-map');
                Route::get('/seats/{seat}/check-availability', [AppointmentController::class, 'checkSeatAvailability'])->name('check-seat-availability');

            });

            Route::prefix('manage-customer')->name('manage-customer.')->group(function () {

                Route::get('/', [CustomerController::class, 'index'])->name('index');
                Route::post('/', [CustomerController::class, 'store'])->name('store');
                Route::get('/show/{customer}', [CustomerController::class, 'show'])->name('show');
                Route::get('/show/{customer}/booking-history', [CustomerController::class, 'bookingHistory'])->name('booking-history');

            });

            // Seat Management Routes
            Route::prefix('seats')->name('seats.')->group(function () {
                Route::get('/', [SeatController::class, 'index'])->name('index');
                Route::get('/create', [SeatController::class, 'create'])->name('create');
                Route::post('/', [SeatController::class, 'store'])->name('store');
                Route::get('/{seat}/edit', [SeatController::class, 'edit'])->name('edit');
                Route::put('/{seat}', [SeatController::class, 'update'])->name('update');
                Route::delete('/{seat}', [SeatController::class, 'destroy'])->name('destroy');
                Route::put('/{seat}/update-status', [SeatController::class, 'updateStatus'])->name('update-status');
            });

            // Service Management Routes
            Route::prefix('services')->name('services.')->group(function () {
                Route::get('/', [ServiceController::class, 'index'])->name('index');
                Route::get('/create', [ServiceController::class, 'create'])->name('create');
                Route::post('/', [ServiceController::class, 'store'])->name('store');
                Route::get('/{service}/edit', [ServiceController::class, 'edit'])->name('edit');
                Route::put('/{service}', [ServiceController::class, 'update'])->name('update');
                Route::delete('/{service}', [ServiceController::class, 'destroy'])->name('destroy');
            });

            // Plan Management Routes
            Route::prefix('plans')->name('plans.')->group(function () {
                Route::get('/', [PlanController::class, 'index'])->name('index');
                Route::get('/create', [PlanController::class, 'create'])->name('create');
                Route::post('/', [PlanController::class, 'store'])->name('store');
                Route::get('/{plan}/edit', [PlanController::class, 'edit'])->name('edit');
                Route::put('/{plan}', [PlanController::class, 'update'])->name('update');
                Route::delete('/{plan}', [PlanController::class, 'destroy'])->name('destroy');
                Route::put('/{plan}/update-status', [PlanController::class, 'updateStatus'])->name('update-status');

            });

            Route::middleware(['role:vendor'])->group(function () {
                // Manage Branch
                Route::prefix('branches')->name('branches.')->group(function () {
                    Route::get('/', [BranchController::class, 'index'])->name('index');
                    Route::get('/create', [BranchController::class, 'create'])->name('create');
                    Route::post('/', [BranchController::class, 'store'])->name('store');
                    Route::get('/{branch}/edit', [BranchController::class, 'edit'])->name('edit');
                    Route::put('/{branch}', [BranchController::class, 'update'])->name('update');
                    Route::delete('/{branch}', [BranchController::class, 'destroy'])->name('destroy');
                    Route::post('/update-current', [BranchController::class, 'updateCurrentBranch']);
                    Route::put('/{branch}/allow-staff', [BranchController::class, 'updateAllowStaff']);

                    // Trashed branches routes
                    Route::get('/trashed', [BranchController::class, 'trashed'])->name('trashed');
                    Route::put('/{id}/restore', [BranchController::class, 'restore'])->name('restore');
                    // Route::delete('/{id}/force-delete', [BranchController::class, 'forceDelete'])->name('force-delete');

                    // Working Hours Routes
                    Route::prefix('working-hours')->name('working-hours.')->group(function () {
                        Route::get('/', [WorkingHourController::class, 'index'])->name('index');
                        Route::get('/save', [WorkingHourController::class, 'create'])->name('save');
                        Route::post('/', [WorkingHourController::class, 'update'])->name('update');
                    });
                });

            });

            // Gallery and Slider Management Pages

            // Staff Management Routes
            Route::prefix('staff')->name('staff.')->group(function () {
                Route::get('/', [StaffController::class, 'index'])->name('index');
                Route::get('/create', [StaffController::class, 'create'])->name('create');
                Route::post('/', [StaffController::class, 'store'])->name('store');
                Route::get('/{user}', [StaffController::class, 'show'])->name('show');
                Route::get('/{user}/edit', [StaffController::class, 'edit'])->name('edit');
                Route::put('/{user}', [StaffController::class, 'update'])->name('update');
                Route::delete('/{user}', [StaffController::class, 'destroy'])->name('destroy');
            });

            // Term & Condition routes
            Route::prefix('terms-conditions')->name('terms-conditions.')->group(function () {
                Route::get('/', [TermConditionController::class, 'index'])->name('index');
                Route::get('/create', [TermConditionController::class, 'create'])->name('create');
                Route::post('/', [TermConditionController::class, 'store'])->name('store');
                Route::get('/{termCondition}/edit', [TermConditionController::class, 'edit'])->name('edit');
                Route::put('/{termCondition}', [TermConditionController::class, 'update'])->name('update');
                Route::delete('/{termCondition}', [TermConditionController::class, 'destroy'])->name('destroy');
                Route::get('/trashed', [TermConditionController::class, 'trashed'])->name('trashed');
                Route::post('/{id}/restore', [TermConditionController::class, 'restore'])->name('restore');
            });

            Route::prefix('purchased-plan')->name('purchased-plan.')->group(function () {
                Route::get('/', [PlanController::class, 'purchases'])->name('purchases');
                Route::post('/{planUsage}/update-status', [PlanController::class, 'updatePlanStatus'])->name('update-status');
            });

            // Gallery and Slider Management Pages
            Route::prefix('media')->name('media.')->group(function () {
                Route::get('/', [BranchMediaController::class, 'index']);
                Route::post('/', [BranchMediaController::class, 'store']);
                Route::put('/{mediaId}', [BranchMediaController::class, 'update']);
                Route::delete('/{mediaId}', [BranchMediaController::class, 'destroy']);
                Route::get('gallery', [BranchMediaController::class, 'galleryPage'])->name('gallery');
                Route::get('slider', [BranchMediaController::class, 'sliderPage'])->name('slider');
            });

        });

    });

    Route::middleware(['auth', 'tenant'])->group(function () {

        Route::middleware(['role:vendor|branchuser|staff'])->group(function () {

            Route::get('/own-appoinment', [UserDashboardController::class, 'index'])->name('own-appoinment');

            Route::prefix('vendor')->name('vendor.')->group(function () {
                // Appointment Management Routes
                Route::prefix('appointments')->name('appointments.')->group(function () {

                    Route::put('/{appointment}/update-status', [AppointmentController::class, 'updateStatus'])->name('update-status');
                    Route::put('/{appointment}/update-seat-status', [AppointmentController::class, 'updateSeatStatus'])->name('update-seat-status');
                    Route::get('/seat-map', [AppointmentController::class, 'seatMap'])->name('seat-map');
                    Route::get('/seats/{seat}/check-availability', [AppointmentController::class, 'checkSeatAvailability'])->name('check-seat-availability');

                });

            });

        });

    });

    // Route::get('/', function () {
    //     return 'This is your multi-tenant application. The id of the current tenant is ' . tenant('id');
    // });

    // Vendor Front Page Routes
    // Route::get('/book', [VendorFrontController::class, 'show'])->name('vendor.front');
    Route::post('/book', [VendorFrontController::class, 'book'])->name('vendor.book');

    Route::prefix('plans')->name('plans.')->group(function () {
        Route::post('/purchase', [VendorFrontController::class, 'purchase'])->name('purchase');
    });

});
