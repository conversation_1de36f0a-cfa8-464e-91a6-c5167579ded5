import React from 'react';
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';
import { setPageTitle } from '@/store/themeConfigSlice';
import { FaUsers, FaCalendarAlt, FaUserTie, FaChartBar, FaGem, FaImages, FaCloud, FaCheck } from 'react-icons/fa';

const featureList = [
  { icon: <FaUsers className="mb-2 text-2xl text-primary" />, label: 'Multi-branch management' },
  { icon: <FaCalendarAlt className="mb-2 text-2xl text-primary" />, label: 'Appointment scheduling' },
  { icon: <FaUserTie className="mb-2 text-2xl text-primary" />, label: 'Staff & service management' },
  { icon: <FaChartBar className="mb-2 text-2xl text-primary" />, label: 'Customer insights & analytics' },
  { icon: <FaGem className="mb-2 text-2xl text-primary" />, label: 'Flexible subscription plans' },
  { icon: <FaImages className="mb-2 text-2xl text-primary" />, label: 'Gallery & slider for your work' },
  { icon: <FaCloud className="mb-2 text-2xl text-primary" />, label: 'Secure, scalable cloud platform' },
];

interface SubscriptionPlan {
  id: number;
  name: string;
  description: string;
  price: number;
  currency_symbol: string;
  currency_code: string;
  billing_cycle: string;
  max_services: number;
  max_appointments_per_month: number;
  max_seats: number;
  max_branches: number;
  max_staff: number;
  has_analytics: boolean;
  has_api_access: boolean;
  has_custom_branding: boolean;
  has_priority_support: boolean;
  sort_order: number;
}

interface PaymentMethod {
  id: number;
  name: string;
  type: string;
  mode: string;
}

interface AuthUser {
  id: number;
  name: string;
  email: string;
  roles: string[];
}

interface ActiveSubscription {
  id: number;
  plan_name: string;
  ends_at: string;
  days_remaining: number;
}

interface Props {
  subscriptionPlans?: SubscriptionPlan[];
  paymentMethods?: PaymentMethod[];
  authUser?: AuthUser | null;
  hasActiveSubscription?: boolean;
  activeSubscription?: ActiveSubscription | null;
  crmInfo?: {
    title: string;
    description: string;
    features: string[];
    cta: string;
  };
}

const AboutSalozy: React.FC<Props> = ({ subscriptionPlans = [], paymentMethods = [], authUser, hasActiveSubscription = false, activeSubscription, crmInfo }) => {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setPageTitle('Welcome to Salozy CRM'));
  }, [dispatch]);

  const handleGetStarted = (planId: number) => {
    // If user is not logged in, redirect to login
    if (!authUser) {
      window.location.href = '/auth/login';
      return;
    }

    // If user is logged in but doesn't have vendor role, redirect to login
    if (!authUser.roles.includes('vendor')) {
      window.location.href = '/auth/login';
      return;
    }

    // Check if user already has an active subscription
    if (hasActiveSubscription) {
      // Show alert instead of proceeding
      alert(`You already have an active subscription to ${activeSubscription?.plan_name} plan that expires on ${activeSubscription?.ends_at} (${activeSubscription?.days_remaining} days remaining).\n\nIf you want to deactivate your current plan and purchase a new one, please contact the administrator for assistance.`);
      return;
    }

    // If user is logged in and has vendor role, proceed to checkout
    window.location.href = `/checkout/subscription/${planId}`;
  };

  return (
    <div className="relative bg-gray-50 w-full min-h-screen font-sans overflow-x-hidden">
      {/* Animated/Gradient Background Blobs */}
      <div className="z-0 absolute inset-0 overflow-hidden pointer-events-none">
        <div className="top-0 left-0 absolute bg-gradient-to-r from-primary/30 to-purple-600/30 opacity-20 blur-3xl rounded-full w-96 h-96 animate-blob"></div>
        <div className="top-1/2 right-0 absolute bg-gradient-to-r from-purple-600/30 to-pink-600/30 opacity-20 blur-3xl rounded-full w-96 h-96 animate-blob animation-delay-2000"></div>
        <div className="bottom-0 left-1/4 absolute bg-gradient-to-r from-pink-600/30 to-primary/30 opacity-20 blur-3xl rounded-full w-96 h-96 animate-blob animation-delay-4000"></div>
      </div>
      {/* Hero Section */}
      <section className="relative z-10 flex flex-col justify-center items-center px-4 py-20 min-h-[90vh] text-center">
        <div className='mb-10 pb-4'>
          <img src="/assets/images/logo-icon.svg" alt="Salozy Logo" className="mx-auto w-32 animate-fade-in" />
        </div>

        <h1 className="mb-4 font-black text-5xl text-gray-800 sm:text-6xl lg:text-5xl">
          Welcome to Salozy CRM
        </h1>
        <p className="mb-2 font-bold text-2xl text-primary sm:text-3xl animate-fade-in">For Salons, Spas, and Beauty Businesses</p>
        <p className="mx-auto mb-8 max-w-2xl text-gray-700 text-lg sm:text-xl animate-fade-in">
          Salozy is your all-in-one platform to manage appointments, staff, branches, services, and customer relationships. Our intuitive dashboard, multi-branch support, and powerful analytics help you grow your business efficiently. Start your free trial today!
        </p>
        {/* Show Sign In and Sign Up buttons only for guest users */}
        {!authUser && (
          <div className="flex sm:flex-row flex-col justify-center gap-6 mb-10 animate-fade-in">
            <a
              href="/auth/login"
              className="inline-block bg-primary hover:bg-primary-dark px-10 py-3 rounded-2xl font-bold text-base text-white transition"
            >
              Sign In
            </a>
            <a
              href="/auth/register"
              className="inline-block bg-gradient-to-r from-pink-500 hover:from-pink-600 to-purple-600 hover:to-purple-700 px-10 py-3 rounded-2xl font-bold text-base text-white transition"
            >
              Sign Up Free
            </a>
          </div>
        )}

        {/* Show welcome message for logged in users */}
        {authUser && (
          <div className="mb-10 animate-fade-in">
            <div className="bg-white/80 backdrop-blur-sm px-8 py-6 border border-primary/20 rounded-2xl">
              <p className="mb-2 text-gray-700 text-xl">
                Welcome back, <span className="font-bold text-primary">{authUser.name}</span>!
              </p>

              {/* Show subscription status */}
              {hasActiveSubscription && activeSubscription ? (
                <div className="mt-3">
                  <div className="items-center mb-2">
                    <p className="font-medium text-green-700">
                      Active Subscription: <span className="font-bold">{activeSubscription.plan_name}</span>
                    </p>
                  </div>
                  <p className="mb-2 text-gray-600">
                    Your subscription is active until <span className="font-medium">{activeSubscription.ends_at}</span> ({activeSubscription.days_remaining} days remaining).
                  </p>
                  <p className="text-blue-600 text-sm">
                    <span className="font-medium">Note:</span> If you want to change your plan or deactivate your current subscription, please contact the administrator for assistance.
                  </p>
                </div>
              ) : (
                <p className="text-gray-600">
                  {authUser.roles.includes('vendor')
                    ? 'Choose a subscription plan below to get started with your business.'
                    : 'Please contact support to upgrade your account to access vendor features.'
                  }
                </p>
              )}
            </div>
          </div>
        )}



      </section>

      {/* Subscription Plans Section */}
      {subscriptionPlans.length > 0 && (
        <section className="relative z-10 bg-white/50 px-4 py-20">
          <div className="mx-auto max-w-7xl">
            {/* Active Subscription Alert */}
            {hasActiveSubscription && activeSubscription && (
              <div className="bg-orange-100 mb-8 p-4 border-orange-500 border-l-4 rounded-r-lg">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-orange-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-orange-700 text-sm">
                      <span className="font-semibold">Active Subscription Found:</span> You currently have an active subscription to <span className="font-semibold">{activeSubscription.plan_name}</span> plan that expires on {activeSubscription.ends_at} ({activeSubscription.days_remaining} days remaining).
                    </p>
                    <p className="mt-1 text-orange-700 text-sm">
                      To deactivate your current plan and purchase a new one, please contact the administrator at <a href="mailto:<EMAIL>" className="font-medium underline"><EMAIL></a>.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="mb-16 text-center">
              <h2 className="mb-4 font-black text-4xl text-gray-800 sm:text-5xl">
                Choose Your Plan
              </h2>
              <p className="mx-auto max-w-2xl text-gray-600 text-lg">
                Select the perfect plan for your business needs. All plans include our core features with different limits and capabilities.
              </p>
            </div>

            <div className="gap-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mx-auto max-w-6xl">
              {subscriptionPlans.map((plan) => (
                <div key={plan.id} className="relative bg-white hover:shadow-sm p-8 border border-gray-200 rounded-3xl transition-all duration-300 hover:scale-105">
                  {plan.sort_order === 2 && (
                    <div className="-top-4 left-1/2 absolute transform -translate-x-1/2">
                      <span className="bg-gradient-to-r from-primary to-purple-600 px-4 py-2 rounded-full font-semibold text-sm text-white">
                        Most Popular
                      </span>
                    </div>
                  )}

                  <div className="mb-8 text-center">
                    <h3 className="mb-2 font-bold text-2xl text-gray-800">{plan.name}</h3>
                    <p className="mb-4 text-gray-600">{plan.description}</p>

                    <div className="mb-2">
                      <span className="font-black text-4xl text-gray-800">{plan.currency_symbol}{plan.price.toLocaleString()}</span>
                      <span className="font-normal text-gray-500 text-lg">/{plan.billing_cycle}</span>
                    </div>
                  </div>

                  <ul className="space-y-3 mb-8">
                    <li className="flex items-center">
                      <FaCheck className="mr-3 text-green-500" />
                      <span>{plan.max_services === 0 ? 'Unlimited' : plan.max_services} Services</span>
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="mr-3 text-green-500" />
                      <span>{plan.max_branches} {plan.max_branches === 1 ? 'Branch' : 'Branches'}</span>
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="mr-3 text-green-500" />
                      <span>{plan.max_staff === 0 ? 'Unlimited' : plan.max_staff} Staff Members</span>
                    </li>
                  </ul>

                  <button
                    onClick={() => handleGetStarted(plan.id)}
                    disabled={hasActiveSubscription}
                    className={`block w-full text-center py-3 px-6 rounded-2xl font-bold text-base transition-all duration-300 ${hasActiveSubscription
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed opacity-70'
                      : plan.sort_order === 2
                        ? 'bg-gradient-to-r from-primary to-purple-600 hover:from-primary-dark hover:to-purple-700 text-white '
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300'
                      }`}
                  >
                    {hasActiveSubscription ? 'Get Started' : 'Get Started'}
                  </button>

                  {/* Info note for active subscription */}
                  {/*{hasActiveSubscription && (*/}
                  {/*  <div className="bg-yellow-50 mt-3 p-2 border border-yellow-200 rounded-lg">*/}
                  {/*    <p className="text-xs text-yellow-700">*/}
                  {/*      You already have an active subscription. To deactivate your current plan and purchase a new one, please contact the administrator.*/}
                  {/*    </p>*/}
                  {/*  </div>*/}
                  {/*)}*/}
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Footer  Section */}
      <div className='px-4 py-6 border-t border-black/10 text-center'>
        <p className="text-base text-gray-500 dark:text-white">© {new Date().getFullYear()} Salozy - All Rights Reserved.</p>
      </div>

    </div>
  );
};

// @ts-expect-error
AboutSalozy.layout = 'blank';

export default AboutSalozy;
