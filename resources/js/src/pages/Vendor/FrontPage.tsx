import React, { useState, Fragment, useEffect, useRef } from 'react';
import { Link, useForm, router } from '@inertiajs/react';
import { Dialog, Transition } from '@headlessui/react';
import Flatpickr from 'react-flatpickr';
import 'flatpickr/dist/flatpickr.css';
import {
    FaCalendarAlt, FaClock, FaUser, FaEnvelope, FaPhone, FaDollarSign,
    FaMinusCircle, FaPlusCircle, FaShoppingCart, FaCheck, FaStar,
    FaMapMarkerAlt, FaInstagram, FaFacebook, FaTwitter, FaBuilding,
    FaTag, FaHeart, FaGift, FaMagic, FaCrown, FaFire, FaGem,
    FaWhatsapp, FaLinkedin, FaYoutube, FaTiktok, FaArrowRight,
    FaPlay, FaQuoteLeft, FaAward, Fa<PERSON>ser<PERSON>, FaThum<PERSON>Up,
    FaPinterest, FaTelegram, FaGlobe
} from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';
import type { FormDataConvertible } from '@inertiajs/core';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';

interface Service {
    id: number;
    name: string;
    description: string;
    duration_minutes: number;
    price: number;
    image: string;
    gender?: 'male' | 'female';
}

interface Plan {
    id: number;
    name: string;
    description: string;
    price: number;
    validity_days: number;
    status: string;
    services: Array<{
        id: number;
        name: string;
        allowed_count: number;
    }>;
}

interface Branch {
    id: number;
    name: string;
    address: string;
}

interface CurrentBranch {
    id: number;
    name: string;
    address: string;
    email: string;
    phone: string;
    social_links: {
        icon: string;
        url: string;
    }[];
    allow_staff?: boolean;
    staff?: Staff[];
}

interface Tenant {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    logo: string;
    branches: Branch[];
}

interface BranchMedia {
    id: number;
    image: string;
    type: string;
    status: boolean;
}

interface WorkingDetail {
    day: string;
    open: string;
    close: string;
    is_closed: boolean;
}

interface PageProps {
    tenant: Tenant;
    services: Service[];
    plans: Plan[];
    current_branch_detail: CurrentBranch;
    selectedBranchId: number;
    layout: string;
    flash?: {
        success?: string;
        error?: string;
        message?: string;
    };
    sliderImages?: BranchMedia[];
    galleryImages?: BranchMedia[];
    branchOpenData?: WorkingDetail[];
    currency_symbol?: string;
    currency_text?: string;
    authUser?: {
        id: number;
        role: string[];
        name: string;
        email: string;
        profile?: string | null;
    } | null;
}

interface FormData extends Record<string, FormDataConvertible> {
    name: string;
    email: string;
    phone: string;
    appointment_date: string;
    appointment_time: string;
    services: number[];
    password: string;
    notes: string;
    branch_id: number;
    staff_id?: number;
    anniversary?: string;
    date_of_birth?: string;
}

interface PlanPurchaseData extends Record<string, FormDataConvertible> {
    name: string;
    email: string;
    phone: string;
    password: string;
    branch_id: number;
    plan_id: number;
    anniversary?: string;
    date_of_birth?: string;
}

interface FormErrors {
    name?: string;
    email?: string;
    phone?: string;
    appointment_date?: string;
    appointment_time?: string;
    password?: string;
    notes?: string;
    branch_id?: string;
    services?: string;
    error?: string;
}

interface Staff {
    id: number;
    name: string;
    email: string;
    profile?: string | null;
}

const SOCIAL_ICONS = [
    { label: 'Facebook', value: 'facebook', Icon: FaFacebook },
    { label: 'Instagram', value: 'instagram', Icon: FaInstagram },
    { label: 'X (Twitter)', value: 'x', Icon: FaXTwitter },
    { label: 'WhatsApp', value: 'whatsapp', Icon: FaWhatsapp },
    { label: 'LinkedIn', value: 'linkedin', Icon: FaLinkedin },
    { label: 'YouTube', value: 'youtube', Icon: FaYoutube },
    { label: 'TikTok', value: 'tiktok', Icon: FaTiktok },
    { label: 'Pinterest', value: 'pinterest', Icon: FaPinterest },
    { label: 'Telegram', value: 'telegram', Icon: FaTelegram },
    { label: 'Website', value: 'website', Icon: FaGlobe },
];

const FrontPage = ({ tenant, services, plans, selectedBranchId, current_branch_detail, layout = 'blank', flash, sliderImages = [], branchOpenData = [], galleryImages = [], currency_symbol = '₹', currency_text = 'INR', authUser = null }: PageProps) => {
    const [selectedServices, setSelectedServices] = useState<Service[]>([]);
    const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
    const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
    const [isPlanPurchaseModalOpen, setIsPlanPurchaseModalOpen] = useState(false);
    const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
    const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const [errorMessage, setErrorMessage] = useState('');
    const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
    const [addedServices, setAddedServices] = useState<Record<number, boolean>>({});
    const [showCart, setShowCart] = useState(false);
    const [selectedBranch, setSelectedBranch] = useState(selectedBranchId);
    const [formErrors, setFormErrors] = useState<Partial<FormErrors>>({});
    const [planFormErrors, setPlanFormErrors] = useState<Partial<FormErrors>>({});
    const servicesRef = useRef<HTMLDivElement>(null);
    const [isMobile, setIsMobile] = useState(false);
    // Gender tab state
    const [selectedGender, setSelectedGender] = useState<'male' | 'female'>('male');
    // User menu state
    const [userMenuOpen, setUserMenuOpen] = useState(false);
    const userMenuRef = useRef<HTMLDivElement>(null);
    // Working hours state for booking modal
    const [workingHours, setWorkingHours] = useState<any[]>(branchOpenData);
    const [minTime, setMinTime] = useState<string>('00:00');
    const [maxTime, setMaxTime] = useState<string>('23:59');
    const [dayClosed, setDayClosed] = useState<boolean>(false);
    const [workingHourError, setWorkingHourError] = useState<string>('');

    // Helper to get today's date in YYYY-MM-DD format
    const getTodayDate = () => {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const { data, setData, errors: formFieldErrors, reset, processing } = useForm<FormData>({
        name: '',
        email: '',
        phone: '',
        appointment_date: getTodayDate(),
        appointment_time: '',
        services: [],
        password: '',
        notes: '',
        branch_id: selectedBranchId,
        staff_id: undefined,
        anniversary: '',
        date_of_birth: '',
    });
    const errors: Partial<FormErrors> = formFieldErrors as Partial<FormErrors>;

    const { data: planPurchaseData, setData: setPlanPurchaseData, errors: planFieldErrors, processing: planProcessing } = useForm<PlanPurchaseData>({
        name: '',
        email: '',
        phone: '',
        password: '',
        branch_id: selectedBranchId,
        plan_id: 0,
        anniversary: '',
        date_of_birth: '',
    });
    const planErrors: Partial<FormErrors> = planFieldErrors as Partial<FormErrors>;

    const updateFormData = (field: keyof FormData, value: FormDataConvertible) => {
        setData((prev) => ({
            ...prev,
            [field]: value
        }));
    };

    // Handle branch selection
    const handleBranchChange = (branchId: number) => {
        setSelectedBranch(branchId);
        setSelectedServices([]); // Clear selected services when branch changes
        setShowCart(false); // Hide cart when branch changes

        // Update URL and reload data
        router.get(
            window.location.pathname,
            { branch_id: branchId },
            { preserveState: true, preserveScroll: true }
        );
    };

    // Add useEffect for mobile detection
    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };
        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    const addToCart = (service: Service) => {
        const updated = [...selectedServices, service];
        setSelectedServices(updated);
        (setData as any)('services', updated.map(s => ({ id: s.id })));
        setAddedServices({ ...addedServices, [service.id]: true });

        // Only show cart popup automatically on desktop
        if (!isMobile) {
            setShowCart(true);
        }

        // Animate service to cart icon on mobile
        if (isMobile) {
            const serviceElement = document.getElementById(`service-${service.id}`);
            const cartIcon = document.querySelector('.mobile-cart-icon');

            if (serviceElement && cartIcon) {
                // Create floating element for animation
                const floatingEl = document.createElement('div');
                const rect = serviceElement.getBoundingClientRect();
                const cartRect = cartIcon.getBoundingClientRect();

                floatingEl.style.cssText = `
                    position: fixed;
                    z-index: 9999;
                    left: ${rect.left}px;
                    top: ${rect.top}px;
                    width: 40px;
                    height: 40px;
                    background: #fc9600;
                    border-radius: 50%;
                    pointer-events: none;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
                `;

                document.body.appendChild(floatingEl);

                // Trigger animation
                setTimeout(() => {
                    floatingEl.style.transform = 'scale(0.5)';
                    floatingEl.style.left = `${cartRect.left + cartRect.width / 2 - 20}px`;
                    floatingEl.style.top = `${cartRect.top + cartRect.height / 2 - 20}px`;
                }, 0);

                // Remove element after animation
                setTimeout(() => {
                    document.body.removeChild(floatingEl);
                    // Add a quick scale animation to cart icon
                    if (cartIcon) {
                        cartIcon.classList.add('scale-125');
                        setTimeout(() => cartIcon.classList.remove('scale-125'), 200);
                    }
                }, 500);
            }
        }
    };

    const removeFromCart = (serviceId: number) => {
        const updated = selectedServices.filter(s => s.id !== serviceId);
        setSelectedServices(updated);
        (setData as any)('services', updated.map(s => ({ id: s.id })));
        setAddedServices({ ...addedServices, [serviceId]: false });

        // If cart is empty after removal, hide the cart popup
        if (updated.length === 0) {
            setShowCart(false);
        }
    };

    const calculateTotal = () => {
        return selectedServices.reduce((sum, service) => sum + service.price, 0);
    };

    const calculateTotalDuration = () => {
        return selectedServices.reduce((sum, service) => sum + service.duration_minutes, 0);
    };

    // Add useEffect to handle flash messages
    useEffect(() => {
        if (flash && typeof flash === 'object') {
            if ('success' in flash && typeof flash.success === 'string' && flash.success) {
                setSuccessMessage(flash.success);
                setIsSuccessModalOpen(true);
                setIsBookingModalOpen(false);
                setIsPlanPurchaseModalOpen(false);
                reset();
                setSelectedServices([]);
            }
            if ('error' in flash && typeof flash.error === 'string' && flash.error) {
                setErrorMessage(flash.error);
                setIsErrorModalOpen(true);
            }
        }
    }, [flash]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        router.post('/book', data, {
            onSuccess: (page) => {
                const flash = (page.props && typeof page.props.flash === 'object') ? page.props.flash as { success?: string; error?: string } : {};
                if (typeof flash.success === 'string' && flash.success) {
                    setSuccessMessage(flash.success);
                    setIsSuccessModalOpen(true);
                    setIsBookingModalOpen(false);
                    setData({
                        name: '',
                        email: '',
                        phone: '',
                        appointment_date: '',
                        appointment_time: '',
                        services: [],
                        password: '',
                        notes: '',
                        branch_id: selectedBranchId,
                        staff_id: undefined,
                        anniversary: '',
                        date_of_birth: '',
                    } as FormData);
                    setSelectedServices([]);
                    setAddedServices({});
                    setShowCart(false);
                }
                if (typeof flash.error === 'string' && flash.error) {
                    setFormErrors(prev => ({
                        ...prev,
                        error: flash.error,
                    }));
                }
            },
            onError: (errors) => {
                // Only assign string errors
                const safeErrors: FormErrors = {};
                for (const key in errors) {
                    if (typeof errors[key] === 'string') {
                        (safeErrors as any)[key] = errors[key];
                    }
                }
                setFormErrors(safeErrors);
            },
        });
    };

    const handlePlanSelect = (plan: Plan) => {
        setSelectedPlan(plan);
        setPlanPurchaseData(data => ({
            ...data,
            plan_id: plan.id
        }));
        setIsPlanPurchaseModalOpen(true);
    };

    const handlePlanPurchase = (e: React.FormEvent) => {
        e.preventDefault();
        router.post('/plans/purchase', planPurchaseData, {
            onSuccess: (page) => {
                const flash = (page.props && typeof page.props.flash === 'object') ? page.props.flash as { success?: string; error?: string } : {};
                if (typeof flash.success === 'string' && flash.success) {
                    setSuccessMessage(flash.success);
                    setIsSuccessModalOpen(true);
                    setIsPlanPurchaseModalOpen(false);
                    setPlanPurchaseData({
                        name: '',
                        email: '',
                        phone: '',
                        password: '',
                        branch_id: selectedBranchId,
                        plan_id: selectedPlan?.id || 0,
                        anniversary: '',
                        date_of_birth: '',
                    } as PlanPurchaseData);
                    setPlanFormErrors({});
                }
                if (typeof flash.error === 'string' && flash.error) {
                    setPlanFormErrors(prev => ({
                        ...prev,
                        error: flash.error,
                    }));
                }
            },
            onError: (errors) => {
                const safeErrors: FormErrors = {};
                for (const key in errors) {
                    if (typeof errors[key] === 'string') {
                        (safeErrors as any)[key] = errors[key];
                    }
                }
                setPlanFormErrors(safeErrors);
            },
        });
    };

    useEffect(() => {
        (setData as any)('branch_id', selectedBranch as number);
        (setPlanPurchaseData as any)('branch_id', selectedBranch as number);
    }, [selectedBranch]);

    // Close menu on outside click
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                setUserMenuOpen(false);
            }
        }
        if (userMenuOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [userMenuOpen]);

    // Helper for profile image URL
    const getProfileImageUrl = (profilePath: string | null | undefined) => {
        if (!profilePath) return '/assets/images/profile-34.jpeg';
        const tenantDomain = window.location.hostname;
        return `https://${tenantDomain}/${profilePath}`;
    };

    // Determine dashboard route
    const isVendor = authUser && Array.isArray(authUser.role) && authUser.role.some(r => r.toLowerCase().includes('vendor'));
    const dashboardRoute = isVendor ? '/vendor/dashboard' : '/user/dashboard';

    // Fetch working hours for a given date and branch
    const fetchWorkingHours = (dateString?: string) => {
        fetch(`/working-hours-get-guest?branch_id=${selectedBranch}`)
            .then(response => {
                if (!response.ok) { throw new Error(`HTTP error! status: ${response.status}`); }
                return response.json();
            })
            .then(data => {
                setWorkingHours(data.workingHours || []);
                // If a date is provided, check the day and time range for that date
                if (dateString) {
                    checkDayAndTimeRange(dateString, data.workingHours || []);
                }
            })
            .catch(error => {
                console.error('Error fetching working hours:', error);
            });
    };

    // Fetch working hours when booking modal opens or branch changes
    useEffect(() => {
        if (isBookingModalOpen && selectedBranch) {
            // Set today's date as default if not already set
            if (!data.appointment_date) {
                const formattedDate = getTodayDate();
                (setData as any)('appointment_date', formattedDate);
                fetchWorkingHours(formattedDate);
            } else {
                fetchWorkingHours(data.appointment_date);
            }
        }
    }, [isBookingModalOpen, selectedBranch]);

    // Helper to get day name from date string (YYYY-MM-DD)
    const getDayName = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { weekday: 'long' });
    };

    // Check day open/close and set time range
    const checkDayAndTimeRange = (dateString: string, workingHoursOverride?: any[]) => {
        if (!dateString) return;
        const dayName = getDayName(dateString);
        const whSource = workingHoursOverride || workingHours;
        const dayWorkingHour = whSource.find((wh) => wh.day === dayName);
        if (dayWorkingHour) {
            if (dayWorkingHour.is_closed) {
                setDayClosed(true);
                setWorkingHourError(`The branch is closed on ${dayName}. You cannot book appointments for this day.`);
                setMinTime('00:00');
                setMaxTime('23:59');
            } else {
                setDayClosed(false);
                setWorkingHourError('');
                setMinTime(dayWorkingHour.open);
                setMaxTime(dayWorkingHour.close);
            }
        } else {
            setDayClosed(true);
            setWorkingHourError(`No working hours found for ${dayName}.`);
            setMinTime('00:00');
            setMaxTime('23:59');
        }
    };

    // When user changes date, update working hour info and fetch latest working hours
    const handleDateChange = (date: Date[]) => {
        if (date[0]) {
            const year = date[0].getFullYear();
            const month = String(date[0].getMonth() + 1).padStart(2, '0');
            const day = String(date[0].getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;
            (setData as any)('appointment_date', formattedDate);
            fetchWorkingHours(formattedDate);
        }
    };

    return (
        <div className="bg-white min-h-screen">
            {/* User Profile Menu at Top Right */}
            {authUser && (
                <div className="top-4 right-4 z-50 fixed" ref={userMenuRef}>
                    <button
                        className="focus:outline-none"
                        onClick={() => setUserMenuOpen((open) => !open)}
                        aria-label="User menu"
                    >
                        <img
                            src={getProfileImageUrl(authUser.profile)}
                            alt="Profile"
                            className="bg-white border-2 border-primary rounded-full w-10 h-10 object-cover"
                        />
                    </button>
                    {userMenuOpen && (
                        <div className="right-0 z-50 absolute bg-white mt-2 py-2 border border-gray-200 rounded-lg w-48">
                            <div className="px-4 py-2 border-gray-200 border-b">
                                <div className="font-medium text-gray-900 truncate text-sm">{authUser.name}</div>
                                <div className="text-gray-500 text-xs truncate">{authUser.email}</div>
                            </div>
                            <a
                                href={dashboardRoute}
                                className="block hover:bg-gray-50 px-4 py-2 text-gray-700 hover:text-primary transition text-sm"
                            >
                                Dashboard
                            </a>
                            <button
                                onClick={(e) => {
                                    e.preventDefault();
                                    router.post('/auth/logout');
                                }}
                                className="hover:bg-gray-50 px-4 py-2 w-full text-gray-700 text-left hover:text-red-600 transition text-sm"
                            >
                                Logout
                            </button>
                        </div>
                    )}
                </div>
            )}
            {/* Branch Selection - Modern Mobile-First Design */}
            {tenant.branches.length > 1 && (
                <div className="top-0 z-40 sticky bg-white/80 backdrop-blur-xl border-b border-black/10 w-full">
                    <div className="flex flex-row justify-between items-center gap-4 px-4 sm:px-8 py-2 w-full min-h-20">
                        {/* Logo on the left */}
                        <div className="flex flex-shrink-0 items-center">
                            {tenant.logo && (
                                <div className="group relative">
                                    <div className="absolute inset-0 opacity-30 group-hover:opacity-50"></div>
                                    <img
                                        src={tenant.logo}
                                        alt={tenant.name}
                                        className="relative w-14 transform transition-all duration-500 group-hover:scale-105"
                                    />
                                </div>
                            )}
                        </div>
                        {/* Branch select on the right */}
                        <div className="flex flex-shrink-0 items-center">
                            <label className="sm:block hidden mr-2 mb-0 font-semibold text-gray-700">Select Branch</label>
                            <select
                                value={selectedBranch}
                                onChange={(e) => handleBranchChange(Number(e.target.value))}
                                className="mr-12 px-4 py-3 border border-black/10 rounded-lg w-40 sm:w-56 font-medium text-sm"
                            >
                                {tenant.branches.map((branch) => (
                                    <option key={branch.id} value={branch.id}>
                                        {branch.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>
            )}

            {/* Hero Section */}
            <div className="relative min-h-[90vh]">
                {/* Slider Background with Overlay */}
                {sliderImages.length > 0 ? (
                    <div className="absolute inset-0">
                        <div className="z-10 absolute inset-0 bg-black/70"></div>
                        <Swiper
                            modules={[Navigation, Pagination, Autoplay]}
                            pagination={{ clickable: true }}
                            autoplay={{ delay: 3500 }}
                            className="h-full"
                        >
                            {sliderImages.map((img) => (
                                <SwiperSlide key={img.id}>
                                    <img src={`/${img.image}`} alt="Slider" className="w-full h-full object-cover" />
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    </div>
                ) : (
                    // Animated background when no slider
                    <div className="absolute inset-0 overflow-hidden">
                        <div className="absolute inset-0 bg-primary/5"></div>
                        <div className="top-0 -left-24 sm:left-0 absolute bg-primary/20 opacity-20 blur-3xl rounded-full w-72 sm:w-96 h-72 sm:h-96 animate-blob filter"></div>
                        <div className="top-1/2 -right-24 sm:right-0 absolute bg-black/20 opacity-20 blur-3xl rounded-full w-72 sm:w-96 h-72 sm:h-96 animate-blob animation-delay-2000 filter"></div>
                        <div className="bottom-0 left-1/4 absolute bg-primary/20 opacity-20 blur-3xl rounded-full w-72 sm:w-96 h-72 sm:h-96 animate-blob animation-delay-4000 filter"></div>
                    </div>
                )}

                {/* Hero Content */}
                <div className="relative z-20 flex items-center py-12 min-h-[90vh]">
                    <div className="mx-auto px-4 container">
                        <div className="mx-auto max-w-5xl">
                            {/* Main Content */}
                            <div className={`text-center space-y-8 ${sliderImages.length > 0 ? 'text-white' : 'text-gray-800'}`}>
                                {/* Logo & Brand */}
                                <div className="flex flex-col items-center gap-6 sm:gap-8">
                                    {tenant.logo && (
                                        <div className="group relative">
                                            <div className="absolute inset-0 group-hover:opacity-50 rounded-3xl transition-opacity duration-300"></div>
                                            <img
                                                src={tenant.logo}
                                                alt={tenant.name}
                                                className="relative p-2 border-4 border-white/30 w-24 sm:w-32 transform transition-all duration-500 group-hover:scale-105"
                                            />
                                        </div>
                                    )}
                                    <div className='space-y-4'>
                                        <h1 className="bg-clip-text px-2 font-black text-4xl sm:text-5xl lg:text-7xl"
                                            style={{
                                                textShadow: sliderImages.length > 0 ? '0 2px 4px rgba(0,0,0,0.3)' : 'none',
                                            }}
                                        >
                                            {current_branch_detail?.name}
                                        </h1>
                                        <p className="opacity-90 text-xl sm:text-2xl">{tenant.name}</p>
                                    </div>
                                </div>

                                {/* Info Cards - Grid for mobile, keeping 3 columns on larger screens */}
                                <div className="flex md:flex-row flex-col justify-center gap-4 sm:gap-6 max-sm:px-4">
                                    <div className={`p-4 sm:p-6 rounded-2xl backdrop-blur-sm w-full md:w-4/12 ${sliderImages.length > 0
                                        ? 'bg-white/10 hover:bg-white/20'
                                        : 'bg-white/80 hover:bg-white/90'
                                        } border border-white/20 transition-all duration-300 group`}>
                                        <div className="space-y-2 sm:space-y-4 text-center">
                                            <FaMapMarkerAlt className="mx-auto text-primary size-8" />
                                            <p className="font-medium text-base sm:text-lg break-words">{current_branch_detail?.address}</p>
                                        </div>
                                    </div>

                                    <div className={`p-4 sm:p-6 rounded-2xl backdrop-blur-sm w-full md:w-4/12 ${sliderImages.length > 0
                                        ? 'bg-white/10 hover:bg-white/20'
                                        : 'bg-white/80 hover:bg-white/90'
                                        } border border-white/20 transition-all duration-300 group`}>
                                        <div className="space-y-2 sm:space-y-4 text-center">
                                            <FaPhone className="mx-auto text-primary size-8" />
                                            <p className="font-medium text-base sm:text-lg break-words">{current_branch_detail?.phone}</p>
                                        </div>
                                    </div>
                                    {current_branch_detail?.email && (
                                        <div className={`p-4 sm:p-6 rounded-2xl backdrop-blur-sm w-full md:w-4/12 ${sliderImages.length > 0
                                            ? 'bg-white/10 hover:bg-white/20'
                                            : 'bg-white/80 hover:bg-white/90'
                                            } border border-white/20 transition-all duration-300 group`}>
                                            <div className="space-y-2 sm:space-y-4 text-center">
                                                <FaEnvelope className="mx-auto text-primary size-8" />
                                                <p className="font-medium sm:text-lg break-all">{current_branch_detail?.email}</p>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* CTA Section - Stack on mobile */}
                                <div className="flex flex-col items-center gap-6 px-4 sm:px-0">
                                    {selectedServices.length > 0 && (
                                        <button
                                            onClick={() => {
                                                if (selectedServices.length === 0 && servicesRef.current) {
                                                    servicesRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                                } else {
                                                    setIsBookingModalOpen(true);
                                                }
                                            }}
                                            className="group relative bg-primary hover:bg-black hover:shadow-2xl px-6 sm:px-8 py-4 rounded-2xl w-full sm:w-auto font-bold text-lg text-white transform transition-all duration-500 overflow-hidden hover:scale-105"
                                        >
                                            <span className="relative flex justify-center items-center gap-3">
                                                <FaCalendarAlt className="w-5 h-5" />
                                                Book Appointment
                                                <FaArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-2 duration-300" />
                                            </span>
                                        </button>
                                    )}
                                    {/* Replace static social icons with dynamic social_links */}
                                    {current_branch_detail?.social_links && current_branch_detail.social_links.length > 0 && (
                                        <div className="flex justify-center items-center gap-4">
                                            {current_branch_detail.social_links.map((link, idx) => {
                                                const Icon = SOCIAL_ICONS.find(i => i.value === link.icon)?.Icon;
                                                return (
                                                    <a
                                                        key={idx}
                                                        href={link.url}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className={`p-3 sm:p-4 rounded-xl sm:rounded-2xl backdrop-blur-sm ${sliderImages.length > 0
                                                            ? 'bg-white/10 hover:bg-white/20'
                                                            : 'bg-white/80 hover:bg-white/90'
                                                            } border border-white/20 transition-all duration-300 transform hover:scale-110`}
                                                        title={SOCIAL_ICONS.find(i => i.value === link.icon)?.label || link.icon}
                                                    >
                                                        {Icon && <Icon className="w-5 sm:w-6 h-5 sm:h-6" />}
                                                    </a>
                                                );
                                            })}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Floating Elements Background */}
            <div className="z-0 fixed inset-0 overflow-hidden pointer-events-none">
                <div className="top-20 left-10 absolute bg-primary/20 blur-xl rounded-full w-32 h-32 animate-pulse"></div>
                <div className="top-40 right-20 absolute bg-black/20 blur-xl rounded-full w-24 h-24 animate-pulse delay-1000"></div>
                <div className="bottom-40 left-20 absolute bg-primary/20 blur-xl rounded-full w-40 h-40 animate-pulse delay-2000"></div>
                <div className="right-10 bottom-20 absolute bg-black/20 blur-xl rounded-full w-28 h-28 animate-pulse delay-500"></div>
            </div>

            {/* Modern Floating Book Button */}
            {selectedServices.length > 0 && (
                <div className="md:block right-4 bottom-4 left-4 z-50 fixed hidden">
                    <div className="bg-white/95 shadow-2xl backdrop-blur-xl p-4 sm:p-6 border border-white/20 rounded-3xl">
                        <div className="flex sm:flex-row flex-col justify-between items-center gap-4">
                            <div className="text-center sm:text-left">
                                <div className="flex justify-center sm:justify-start items-center gap-2 mb-1">
                                    <FaShoppingCart className="text-primary" />
                                    <span className="font-medium text-gray-600">{selectedServices.length} services selected</span>
                                </div>
                                <div className="flex justify-center sm:justify-start items-center gap-2">
                                    <FaGem className="text-primary text-sm" />
                                    <span className="font-bold text-primary text-xl">{currency_symbol}{calculateTotal()}</span>
                                    <span className="text-gray-500 text-sm">• {calculateTotalDuration()} mins</span>
                                </div>
                            </div>
                            <div className="flex items-center gap-3">
                                <button
                                    onClick={() => setShowCart(true)}
                                    className="inline-flex justify-center items-center bg-primary/10 hover:bg-primary/20 px-6 py-4 rounded-2xl focus:ring-4 focus:ring-primary/30 font-bold text-lg text-primary transition-all duration-300 focus:outline-none"
                                >
                                    <FaShoppingCart className="mr-2 w-5 h-5" />
                                    View Cart
                                </button>
                                <button
                                    onClick={() => setIsBookingModalOpen(true)}
                                    className="inline-flex justify-center items-center bg-primary hover:bg-black hover:shadow-xl px-8 py-4 rounded-2xl focus:ring-4 focus:ring-primary/30 font-bold text-lg text-white transform transition-all hover:-translate-y-1 duration-300 focus:outline-none hover:scale-105"
                                >
                                    <FaCalendarAlt className="mr-3 w-5 h-5" />
                                    Book Now
                                    <FaArrowRight className="ml-3 w-4 h-4" />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Modern Mobile Cart Popup */}
            <Transition
                show={showCart}
                enter="transition ease-out duration-500"
                enterFrom="translate-x-full opacity-0"
                enterTo="translate-x-0 opacity-100"
                leave="transition ease-in duration-300"
                leaveFrom="translate-x-0 opacity-100"
                leaveTo="translate-x-full opacity-0"
            >
                <div className="top-20 right-2 z-50 fixed flex flex-col bg-white/95 shadow-2xl backdrop-blur-xl p-4 sm:p-6 border border-white/20 rounded-3xl w-[calc(100vw-1rem)] sm:w-96 max-h-[80vh]">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="flex items-center font-bold text-primary text-xl">
                            <div className="bg-primary mr-3 p-2 rounded-xl">
                                <FaShoppingCart className="text-sm text-white" />
                            </div>
                            Your Cart
                        </h3>
                        <button
                            onClick={() => setShowCart(false)}
                            className="bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors duration-200"
                        >
                            <FaMinusCircle className="w-5 h-5 text-gray-600" />
                        </button>
                    </div>
                    <div className="flex-1 space-y-3 overflow-y-auto scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent">
                        {selectedServices.map((service) => (
                            <div key={service.id} className="bg-primary/5 hover:shadow-lg p-4 border border-black/10 rounded-2xl transition-all duration-300">
                                <div className="flex justify-between items-center">
                                    <div className="flex-1">
                                        <h4 className="font-semibold text-gray-800 text-sm sm:text-base">{service.name}</h4>
                                        <p className="mt-1 text-gray-500 text-xs">
                                            <FaClock className="inline mr-1" />
                                            {service.duration_minutes} mins
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <span className="font-bold text-lg text-primary">{currency_symbol}{service.price}</span>
                                        <button
                                            onClick={() => removeFromCart(service.id)}
                                            className="bg-red-50 hover:bg-red-100 p-2 rounded-full text-red-500 hover:text-red-600 transition-colors duration-200"
                                        >
                                            <FaMinusCircle className="w-4 h-4" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                    {selectedServices.length > 0 && (
                        <>
                            <div className="mt-6 pt-4 border-gray-200/50 border-t">
                                <div className="bg-primary/10 p-4 rounded-2xl">
                                    <div className="flex justify-between items-center mb-2">
                                        <span className="flex items-center font-bold text-gray-700 text-lg">
                                            <FaClock className="mr-2 text-primary" />
                                            Total Duration:
                                        </span>
                                        <span className="font-black text-gray-800 text-xl">{calculateTotalDuration()} mins</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="flex items-center font-bold text-gray-700 text-lg">
                                            <FaGem className="mr-2 text-primary" />
                                            Total Amount:
                                        </span>
                                        <span className="font-black text-3xl text-primary">{currency_symbol}{calculateTotal()}</span>
                                    </div>
                                </div>
                            </div>
                            <div className="mt-4">
                                <button
                                    onClick={() => {
                                        setShowCart(false);
                                        setIsBookingModalOpen(true);
                                    }}
                                    className="group relative bg-primary hover:bg-black hover:shadow-xl px-6 py-4 rounded-2xl focus:ring-4 focus:ring-primary/30 w-full font-bold text-lg text-white transform transition-all duration-300 overflow-hidden focus:outline-none hover:scale-105"
                                >
                                    <div className="relative flex justify-center items-center gap-3">
                                        <FaCalendarAlt className="w-5 h-5" />
                                        <span>Proceed to Book</span>
                                        <FaArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1 duration-300" />
                                    </div>
                                </button>
                            </div>
                        </>
                    )}
                </div>
            </Transition>

            {/* Gallery Carousel below hero */}
            {galleryImages.length > 0 && (
                <div className="mx-auto px-4 sm:px-6 lg:px-8 py-12 max-w-7xl">
                    {/* <h2 className="bg-clip-text bg-gradient-to-r from-primary to-purple-600 mb-6 font-bold text-3xl text-center text-transparent">Gallery</h2> */}
                    <Swiper
                        modules={[Navigation, Pagination]}
                        navigation
                        pagination={{ clickable: true }}
                        breakpoints={{
                            1024: { slidesPerView: 3, spaceBetween: 30 },
                            768: { slidesPerView: 2, spaceBetween: 20 },
                            320: { slidesPerView: 1, spaceBetween: 10 },
                        }}
                        className="w-full swiper"
                    >
                        {galleryImages.map((img) => (
                            <SwiperSlide key={img.id}>
                                <img
                                    src={`/${img.image}`}
                                    alt="Gallery"
                                    className="shadow-lg rounded-2xl w-full h-64 object-cover"
                                />
                            </SwiperSlide>
                        ))}
                    </Swiper>
                </div>
            )}

            {/* Modern Membership Plans Section */}
            {plans.length > 0 && (
                <div className="relative py-20 sm:py-20">
                    <div className="mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
                        {/* Section Header */}
                        <div className="mb-16 text-center">
                            <div className="inline-flex justify-center items-center bg-primary mb-6 rounded-2xl w-16 h-16">
                                <FaCrown className="w-8 h-8 text-white" />
                            </div>
                            <h2 className="mb-6 font-black text-4xl sm:text-5xl lg:text-6xl">
                                <span className="text-primary">
                                    Membership Plans
                                </span>
                            </h2>
                            <p className="mx-auto max-w-3xl text-gray-600 text-xl leading-relaxed">
                                💎 Unlock exclusive benefits and save more with our premium membership plans
                            </p>
                        </div>

                        {/* Plans Grid */}
                        <div className="gap-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                            {plans.map((plan, index) => (
                                <div
                                    key={plan.id}
                                    className={`relative group ${index === 1 ? 'lg:scale-110 lg:z-10' : ''}`}
                                >
                                    {/* Popular Badge */}
                                    {index === 1 && (
                                        <div className="-top-4 left-1/2 z-20 absolute transform -translate-x-1/2">
                                            <div className="bg-primary shadow-lg px-6 py-2 rounded-full font-bold text-sm text-white">
                                                🔥 Most Popular
                                            </div>
                                        </div>
                                    )}

                                    <div className="relative bg-white/90 hover:shadow-3xl backdrop-blur-xl border border-black/10 rounded-3xl transition-all group-hover:-translate-y-2 duration-500 overflow-hidden">
                                        {/* Plan Header */}
                                        <div className={`p-8 ${index === 1 ? 'bg-primary/20' : 'bg-gray-50'}`}>
                                            <div className="flex justify-between items-center mb-4">
                                                <h3 className="font-bold text-2xl text-gray-900">{plan.name}</h3>
                                                <div className="bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full">
                                                    <span className="font-bold text-primary text-sm">{plan.validity_days} Days</span>
                                                </div>
                                            </div>

                                            <div className="flex items-baseline gap-2 mb-4">
                                                <span className="font-black text-4xl text-primary">{currency_symbol}{plan.price}</span>
                                                <span className="text-gray-500 text-lg">/plan</span>
                                            </div>

                                            <p className="text-gray-600 leading-relaxed">{plan.description}</p>
                                        </div>

                                        {/* Services List */}
                                        <div className="p-8">
                                            <div className="space-y-4 mb-8">
                                                <h4 className="flex items-center font-bold text-gray-900 text-lg">
                                                    <FaGem className="mr-3 text-primary" />
                                                    Included Services:
                                                </h4>
                                                {plan.services.map((service) => (
                                                    <div key={service.id} className="flex justify-between items-center bg-primary/5 p-4 border border-black/10 rounded-2xl">
                                                        <div className="flex items-center gap-3">
                                                            <FaCheck className="text-green-500 text-sm" />
                                                            <span className="font-medium text-gray-700">{service.name}</span>
                                                        </div>
                                                        <div className="bg-primary/10 px-3 py-1 rounded-full">
                                                            <span className="font-bold text-primary text-sm">{service.allowed_count}x</span>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>

                                            {/* CTA Button */}
                                            <button
                                                onClick={() => handlePlanSelect(plan)}
                                                className={`w-full group relative overflow-hidden font-bold py-4 px-6 rounded-2xl transition-all duration-500 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary/30 ${index === 1
                                                    ? 'bg-primary text-white hover:bg-black hover:shadow-2xl'
                                                    : 'bg-gray-800 text-white hover:bg-primary'
                                                    }`}
                                            >
                                                <div className="relative flex justify-center items-center gap-3">
                                                    <FaGift className="w-5 h-5" />
                                                    <span>Choose This Plan</span>
                                                    <FaArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1 duration-300" />
                                                </div>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Modern Services Section */}
            {services.length > 0 && (
                <div ref={servicesRef} className="relative bg-white py-20 sm:py-20">
                    <div className="mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
                        {/* Section Header */}
                        <div className="mb-16 text-center">
                            <div className="inline-flex justify-center items-center bg-primary mb-6 rounded-2xl w-16 h-16">
                                <FaMagic className="w-8 h-8 text-white" />
                            </div>
                            <h2 className="mb-6 font-black text-4xl sm:text-5xl lg:text-6xl">
                                <span className="text-primary">
                                    Our Services
                                </span>
                            </h2>
                            <p className="mx-auto max-w-3xl text-gray-600 text-xl leading-relaxed">
                                ✨ Discover our premium beauty services crafted by expert professionals
                            </p>
                        </div>
                        {/* Gender Tabs */}
                        <div className="flex justify-center gap-4 mb-10">
                            <button
                                className={`px-6 py-3 rounded-full font-bold text-lg transition-all duration-200 border-2 focus:outline-none ${selectedGender === 'male' ? 'bg-primary text-white border-primary shadow-lg' : 'bg-white text-primary border-primary/30 hover:bg-primary/10'}`}
                                onClick={() => setSelectedGender('male')}
                            >
                                Male
                            </button>
                            <button
                                className={`px-6 py-3 rounded-full font-bold text-lg transition-all duration-200 border-2 focus:outline-none ${selectedGender === 'female' ? 'bg-black text-white border-black shadow-lg' : 'bg-white text-black border-black/30 hover:bg-black/10'}`}
                                onClick={() => setSelectedGender('female')}
                            >
                                Female
                            </button>
                        </div>
                        {/* Services Grid */}
                        <div className="gap-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 stagger-animation">
                            {services.filter(service => service.gender === selectedGender).length === 0 ? (
                                <div className="col-span-full py-12 font-semibold text-center text-gray-400 text-xl">
                                    {selectedGender === 'male' ? 'No male services available.' : 'No female services available.'}
                                </div>
                            ) : (
                                <>
                                    {services.filter(service => service.gender === selectedGender).map((service, index) => (
                                        <div
                                            key={service.id}
                                            className="group relative flex flex-col justify-between bg-white/90 hover:shadow-3xl backdrop-blur-xl border border-black/10 rounded-3xl transition-all hover:-translate-y-3 duration-500 overflow-hidden service-card card-hover"
                                        >
                                            {/* Service Content - No Image */}
                                            <div className="flex flex-col flex-1 gap-2 p-8">
                                                <div className="flex items-center gap-4 mb-2">
                                                    <div className="bg-primary p-3 rounded-2xl">
                                                        <FaMagic className="w-7 h-7 text-white" />
                                                    </div>
                                                    <h3 className="font-bold text-2xl text-gray-900 group-hover:text-primary transition-colors duration-300">
                                                        {service.name}
                                                    </h3>
                                                </div>
                                                <p className="mb-2 text-base text-gray-600 leading-relaxed line-clamp-4">
                                                    {service.description}
                                                </p>
                                                <div className="flex items-center gap-4 mt-4">
                                                    <div className="flex items-center gap-2 bg-primary/10 px-4 py-2 rounded-xl">
                                                        <FaClock className="w-4 h-4 text-primary" />
                                                        <span className="font-medium text-gray-700 text-sm">{service.duration_minutes} mins</span>
                                                    </div>
                                                    <div className="flex items-center gap-2 bg-black/10 px-4 py-2 rounded-xl">
                                                        <FaGem className="w-4 h-4 text-black" />
                                                        <span className="font-bold text-lg text-primary">{currency_symbol}{service.price}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            {/* Price and Action */}
                                            <div className="flex justify-between items-center px-6 py-4 border-gray-100 border-t">
                                                <span className="text-gray-500 text-sm">per session</span>
                                                <button
                                                    id={`service-${service.id}`}
                                                    onClick={() => addToCart(service)}
                                                    disabled={selectedServices.some(s => s.id === service.id) || addedServices[service.id]}
                                                    className={`btn-modern group/btn relative overflow-hidden px-6 py-3 rounded-2xl !shadow-none font-bold transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary/30 touch-target ${selectedServices.some(s => s.id === service.id) || addedServices[service.id]
                                                        ? 'bg-green-500 text-white cursor-not-allowed'
                                                        : 'bg-primary hover:bg-black text-white hover:shadow-xl animate-pulse-glow'
                                                        }`}
                                                >
                                                    <div className="relative flex justify-center items-center gap-2">
                                                        {selectedServices.some(s => s.id === service.id) || addedServices[service.id] ? (
                                                            <>
                                                                <FaCheck className="w-4 h-4" />
                                                                <span>Added</span>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <FaHeart className="w-4 h-4 transition-transform duration-300 group-hover/btn:scale-110" />
                                                                <span>Add</span>
                                                            </>
                                                        )}
                                                    </div>
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* Modern Booking Modal */}
            <Transition appear show={isBookingModalOpen} as={Fragment}>
                <Dialog
                    as="div"
                    className="z-50 fixed inset-0 overflow-y-auto"
                    onClose={() => setIsBookingModalOpen(false)}
                >
                    <div className="px-4 min-h-screen text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-500"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="ease-in duration-300"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <div className="fixed inset-0 bg-black/60 backdrop-blur-md" />
                        </Transition.Child>

                        <span className="inline-block h-screen align-middle" aria-hidden="true">
                            &#8203;
                        </span>

                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-500"
                            enterFrom="opacity-0 scale-90 translate-y-8"
                            enterTo="opacity-100 scale-100 translate-y-0"
                            leave="ease-in duration-300"
                            leaveFrom="opacity-100 scale-100 translate-y-0"
                            leaveTo="opacity-0 scale-90 translate-y-8"
                        >
                            <div className="inline-block bg-white/95 shadow-2xl backdrop-blur-xl my-8 p-6 sm:p-8 border border-white/30 rounded-3xl sm:rounded-3xl w-full max-w-4xl text-left transform transition-all overflow-hidden align-middle mobile-modal">
                                <div className="sm:block mobile-modal-content">
                                    {/* Modal Header */}
                                    <div className="flex justify-between items-center mb-4 pb-4 border-gray-200/50 border-b">
                                        <div className="flex items-center gap-4">
                                            <div className="bg-primary p-3 rounded-2xl">
                                                <FaCalendarAlt className="w-6 h-6 text-white" />
                                            </div>
                                            <div>
                                                <Dialog.Title as="h3" className="font-black text-3xl text-primary">
                                                    Book Your Appointment
                                                </Dialog.Title>
                                                <p className="mt-1 text-gray-600">Fill in your details to secure your booking</p>
                                            </div>
                                        </div>
                                        <button
                                            onClick={() => setIsBookingModalOpen(false)}
                                            className="bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors duration-200"
                                        >
                                            <FaMinusCircle className="w-6 h-6 text-gray-600" />
                                        </button>
                                    </div>

                                    {/* Form Content */}
                                    <div className="space-y-8">
                                        <form onSubmit={handleSubmit} className="space-y-4">
                                            {/* Personal Information */}
                                            <div className="bg-primary/5 p-4 border border-black/10 rounded-2xl">
                                                <h4 className="flex items-center mb-2 font-bold text-gray-900 text-xl">
                                                    <FaUser className="mr-3 text-primary" />
                                                    Personal Information
                                                </h4>
                                                <div className="gap-6 grid grid-cols-1 sm:grid-cols-2">
                                                    {authUser ? (
                                                        <>
                                                            {/* Hidden fields for logged-in user */}
                                                            <input type="hidden" name="user_id" value={authUser.id} />
                                                            <input type="hidden" name="user_role" value={authUser.role} />
                                                            <div className="col-span-2 font-semibold text-green-700">You are booking as: {authUser.name} ({authUser.email})</div>
                                                        </>
                                                    ) : (
                                                        <>
                                                            <div>
                                                                <label htmlFor="name" className="block mb-3 font-bold text-gray-700 text-sm">
                                                                    Full Name *
                                                                </label>
                                                                <input
                                                                    type="text"
                                                                    id="name"
                                                                    value={data.name}
                                                                    onChange={e => (setData as any)('name', e.target.value)}
                                                                    className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900 placeholder-gray-500"
                                                                    placeholder="Enter your full name"
                                                                />
                                                                {(errors.name || formErrors.name) && (
                                                                    <p className="flex items-center mt-2 text-red-600 text-sm">
                                                                        <span className="mr-1">⚠️</span> {errors.name || formErrors.name}
                                                                    </p>
                                                                )}
                                                            </div>

                                                            <div>
                                                                <label htmlFor="email" className="block mb-3 font-bold text-gray-700 text-sm">
                                                                    Email Address *
                                                                </label>
                                                                <input
                                                                    type="email"
                                                                    id="email"
                                                                    value={data.email}
                                                                    onChange={e => (setData as any)('email', e.target.value)}
                                                                    className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900 placeholder-gray-500"
                                                                    placeholder="<EMAIL>"
                                                                />
                                                                {(errors.email || formErrors.email) && (
                                                                    <p className="flex items-center mt-2 text-red-600 text-sm">
                                                                        <span className="mr-1">⚠️</span> {errors.email || formErrors.email}
                                                                    </p>
                                                                )}
                                                            </div>

                                                            <div>
                                                                <label htmlFor="phone" className="block mb-3 font-bold text-gray-700 text-sm">
                                                                    Phone Number *
                                                                </label>
                                                                <input
                                                                    type="tel"
                                                                    id="phone"
                                                                    value={data.phone}
                                                                    onChange={e => (setData as any)('phone', e.target.value)}
                                                                    className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900 placeholder-gray-500"
                                                                    placeholder="+****************"
                                                                />
                                                                {(errors.phone || formErrors.phone) && (
                                                                    <p className="flex items-center mt-2 text-red-600 text-sm">
                                                                        <span className="mr-1">⚠️</span> {errors.phone || formErrors.phone}
                                                                    </p>
                                                                )}
                                                            </div>

                                                            <div>
                                                                <label htmlFor="password" className="block mb-3 font-bold text-gray-700 text-sm">
                                                                    Password (for new accounts)
                                                                </label>
                                                                <input
                                                                    type="password"
                                                                    id="password"
                                                                    value={data.password}
                                                                    onChange={e => (setData as any)('password', e.target.value)}
                                                                    className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900 placeholder-gray-500"
                                                                    placeholder="Create a secure password"
                                                                />
                                                                {(errors.password || formErrors.password) && (
                                                                    <p className="flex items-center mt-2 text-red-600 text-sm">
                                                                        <span className="mr-1">⚠️</span> {errors.password || formErrors.password}
                                                                    </p>
                                                                )}
                                                            </div>
                                                            {!authUser && (
                                                                <>
                                                                    <div>
                                                                        <label htmlFor="anniversary" className="block mb-3 font-bold text-gray-700 text-sm">
                                                                            Anniversary (optional)
                                                                        </label>
                                                                        <input
                                                                            type="date"
                                                                            id="anniversary"
                                                                            value={data.anniversary ? String(data.anniversary).slice(0, 10) : ''}
                                                                            onChange={e => (setData as any)('anniversary', e.target.value)}
                                                                            className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900 placeholder-gray-500"
                                                                            placeholder="Anniversary date"
                                                                        />
                                                                    </div>
                                                                    <div>
                                                                        <label htmlFor="date_of_birth" className="block mb-3 font-bold text-gray-700 text-sm">
                                                                            Date of Birth (optional)
                                                                        </label>
                                                                        <input
                                                                            type="date"
                                                                            id="date_of_birth"
                                                                            value={data.date_of_birth ? String(data.date_of_birth).slice(0, 10) : ''}
                                                                            onChange={e => (setData as any)('date_of_birth', e.target.value)}
                                                                            className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900 placeholder-gray-500"
                                                                            placeholder="Date of birth"
                                                                        />
                                                                    </div>
                                                                </>
                                                            )}
                                                        </>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Appointment Details */}
                                            <div className="bg-blue-50 p-4 border border-black/10 rounded-2xl">
                                                <h4 className="flex items-center mb-3 font-bold text-gray-900 text-xl">
                                                    <FaCalendarAlt className="mr-3 text-primary" />
                                                    Appointment Details
                                                </h4>
                                                <div className="gap-6 grid grid-cols-1 sm:grid-cols-2">
                                                    {/* Staff selection if enabled */}
                                                    {current_branch_detail?.allow_staff && current_branch_detail?.staff && current_branch_detail.staff.length > 0 && (
                                                        <div className="sm:col-span-2">
                                                            <label htmlFor="staff_id" className="block mb-3 font-bold text-gray-700 text-sm">
                                                                Select Staff (Optional)
                                                            </label>
                                                            <select
                                                                id="staff_id"
                                                                value={data.staff_id || ''}
                                                                onChange={e => (setData as any)('staff_id', e.target.value ? Number(e.target.value) : undefined)}
                                                                className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900"
                                                            >
                                                                <option value="">Any Staff</option>
                                                                {current_branch_detail.staff.map(staff => (
                                                                    <option key={staff.id} value={staff.id}>
                                                                        {staff.name} ({staff.email})
                                                                    </option>
                                                                ))}
                                                            </select>
                                                            {/* Optionally show staff profile image */}
                                                            {data.staff_id && (
                                                                <div className="flex items-center gap-2 mt-2">
                                                                    {(() => {
                                                                        const staff = current_branch_detail.staff.find(s => s.id === data.staff_id);
                                                                        if (!staff) return null;
                                                                        return (
                                                                            <>
                                                                                <img src={staff.profile ? `/${staff.profile}` : '/assets/images/profile-34.jpeg'} alt={staff.name} className="border rounded-full w-10 h-10" />
                                                                                <span className="font-medium text-gray-700">{staff.name}</span>
                                                                            </>
                                                                        );
                                                                    })()}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                    <div>
                                                        <label htmlFor="appointment_date" className="block mb-3 font-bold text-gray-700 text-sm">
                                                            Preferred Date *
                                                        </label>
                                                        <Flatpickr
                                                            id="appointment_date"
                                                            value={data.appointment_date}
                                                            onChange={handleDateChange}
                                                            options={{
                                                                dateFormat: 'Y-m-d',
                                                                minDate: 'today',
                                                                disableMobile: true,
                                                            }}
                                                            className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900"
                                                            placeholder="Select your preferred date"
                                                        />
                                                        {(errors.appointment_date || formErrors.appointment_date) && (
                                                            <p className="flex items-center mt-2 text-red-600 text-sm">
                                                                <span className="mr-1">⚠️</span> {errors.appointment_date || formErrors.appointment_date}
                                                            </p>
                                                        )}
                                                    </div>

                                                    <div>
                                                        <label htmlFor="appointment_time" className="block mb-3 font-bold text-gray-700 text-sm">
                                                            Preferred Time *
                                                        </label>
                                                        <Flatpickr
                                                            id="appointment_time"
                                                            value={data.appointment_time}
                                                            onChange={(time) => {
                                                                if (time[0]) {
                                                                    setData('appointment_time', time[0].toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true }));
                                                                }
                                                            }}
                                                            options={{
                                                                enableTime: true,
                                                                noCalendar: true,
                                                                dateFormat: 'h:i K', // 12-hour format with AM/PM
                                                                time_24hr: false,
                                                                disableMobile: true,
                                                                minTime: minTime,
                                                                maxTime: maxTime,
                                                                minuteIncrement: 15,
                                                            }}
                                                            className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900"
                                                            placeholder="Select your preferred time"
                                                            disabled={dayClosed}
                                                        />
                                                        {(errors.appointment_time || formErrors.appointment_time) && (
                                                            <p className="flex items-center mt-2 text-red-600 text-sm">
                                                                <span className="mr-1">⚠️</span> {errors.appointment_time || formErrors.appointment_time}
                                                            </p>
                                                        )}
                                                        {/* Show error if day is closed */}
                                                        {workingHourError && (
                                                            <div className="mt-2 mb-2 text-danger text-red-600">{workingHourError}</div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Additional Notes */}
                                            <div className="bg-green-50 p-4 border border-black/10 rounded-2xl">
                                                <label htmlFor="notes" className="block mb-3 font-bold text-gray-700 text-sm">
                                                    Special Requests or Notes
                                                </label>
                                                <textarea
                                                    id="notes"
                                                    rows={4}
                                                    value={data.notes}
                                                    onChange={e => (setData as any)('notes', e.target.value)}
                                                    className="block bg-white/80 backdrop-blur-sm px-4 py-4 border focus:border-transparent border-black/10 rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900 placeholder-gray-500 resize-none"
                                                    placeholder="Tell us about any special requests, allergies, or preferences..."
                                                />
                                                {(errors.notes || formErrors.notes) && (
                                                    <p className="flex items-center mt-2 text-red-600 text-sm">
                                                        <span className="mr-1">⚠️</span> {errors.notes || formErrors.notes}
                                                    </p>
                                                )}
                                            </div>

                                            {/* Service Selection Summary */}
                                            <div className="bg-primary/5 p-4 border border-black/10 rounded-2xl">
                                                <h4 className="flex items-center mb-3 font-bold text-gray-900 text-xl">
                                                    <FaShoppingCart className="mr-3 text-primary" />
                                                    Your Selected Services
                                                </h4>

                                                {selectedServices.length === 0 ? (
                                                    <div className="py-12 text-center">
                                                        <FaMagic className="mx-auto mb-4 w-16 h-16 text-gray-300" />
                                                        <p className="font-medium text-gray-500 text-lg">No services selected yet</p>
                                                        <p className="mt-2 text-gray-400 text-sm">Add services from our collection above!</p>
                                                    </div>
                                                ) : (
                                                    <div className="space-y-4">
                                                        {selectedServices.map((service) => (
                                                            <div key={service.id} className="flex justify-between items-center bg-white/80 hover:shadow-lg backdrop-blur-sm p-4 border border-black/10 rounded-2xl transition-all duration-300">
                                                                <div className="flex items-center gap-4">
                                                                    <div className="bg-primary p-2 rounded-xl">
                                                                        <FaMagic className="w-4 h-4 text-white" />
                                                                    </div>
                                                                    <div>
                                                                        <h5 className="font-bold text-gray-800 text-lg">{service.name}</h5>
                                                                        <p className="flex items-center gap-1 text-gray-500 text-sm">
                                                                            <FaClock className="w-3 h-3" />
                                                                            {service.duration_minutes} minutes
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                                <div className="flex items-center gap-4">
                                                                    <span className="font-black text-primary text-xl">{currency_symbol}{service.price}</span>
                                                                    <button
                                                                        type="button"
                                                                        onClick={() => removeFromCart(service.id)}
                                                                        className="bg-red-50 hover:bg-red-100 p-2 rounded-full text-red-500 hover:text-red-600 transition-colors duration-200"
                                                                    >
                                                                        <FaMinusCircle className="w-5 h-5" />
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        ))}

                                                        {/* Total Summary */}
                                                        <div className="bg-primary/10 mt-4 p-4 border-2 border-primary/20 rounded-2xl">
                                                            <div className="flex justify-between items-center mb-3">
                                                                <span className="flex items-center font-bold text-gray-700 text-lg">
                                                                    <FaClock className="mr-2 text-primary" />
                                                                    Total Duration:
                                                                </span>
                                                                <span className="font-black text-gray-800 text-xl">{calculateTotalDuration()} mins</span>
                                                            </div>
                                                            <div className="flex justify-between items-center">
                                                                <span className="flex items-center font-bold text-gray-700 text-lg">
                                                                    <FaGem className="mr-2 text-primary" />
                                                                    Total Amount:
                                                                </span>
                                                                <span className="font-black text-3xl text-primary">{currency_symbol}{calculateTotal()}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>

                                            {/* General Error Message */}
                                            {formErrors.error && (
                                                <div className="bg-red-50 px-4 py-3 border border-red-200 rounded-xl text-red-600">
                                                    <p className="flex items-center">
                                                        <span className="mr-2">⚠️</span>
                                                        {formErrors.error}
                                                    </p>
                                                </div>
                                            )}

                                            {/* Action Buttons */}
                                            <div className="flex sm:flex-row flex-col gap-4 pt-2">
                                                <button
                                                    type="button"
                                                    onClick={() => setIsBookingModalOpen(false)}
                                                    className="flex-1 sm:flex-none bg-white/80 hover:bg-gray-50 backdrop-blur-sm px-8 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-gray-200/50 font-bold text-gray-700 text-lg transform transition-all duration-300 focus:outline-none hover:scale-105"
                                                >
                                                    Cancel
                                                </button>
                                                <button
                                                    type="submit"
                                                    disabled={processing || selectedServices.length === 0 || dayClosed}
                                                    className="group relative flex-1 bg-primary hover:bg-black disabled:opacity-50 hover:shadow-2xl px-8 py-4 rounded-2xl focus:ring-4 focus:ring-primary/30 font-bold text-lg text-white transform transition-all duration-500 disabled:cursor-not-allowed overflow-hidden focus:outline-none hover:scale-105 disabled:hover:scale-100"
                                                >
                                                    <div className="relative flex justify-center items-center gap-3">
                                                        {processing ? (
                                                            <>
                                                                <div className="border-white border-b-2 rounded-full w-5 h-5 animate-spin"></div>
                                                                <span>Processing...</span>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <FaCheck className="w-5 h-5" />
                                                                <span>Confirm Booking</span>
                                                                <FaArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1 duration-300" />
                                                            </>
                                                        )}
                                                    </div>
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </Transition.Child>
                    </div>
                </Dialog>
            </Transition>

            {/* Plan Purchase Modal */}
            <Transition appear show={isPlanPurchaseModalOpen} as={Fragment}>
                <Dialog
                    as="div"
                    className="z-50 fixed inset-0 overflow-y-auto"
                    onClose={() => setIsPlanPurchaseModalOpen(false)}
                >
                    <div className="px-4 min-h-screen text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-500"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="ease-in duration-300"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <div className="fixed inset-0 bg-black/60 backdrop-blur-md" />
                        </Transition.Child>

                        <span className="inline-block h-screen align-middle" aria-hidden="true">
                            &#8203;
                        </span>

                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-500"
                            enterFrom="opacity-0 scale-90 translate-y-8"
                            enterTo="opacity-100 scale-100 translate-y-0"
                            leave="ease-in duration-300"
                            leaveFrom="opacity-100 scale-100 translate-y-0"
                            leaveTo="opacity-0 scale-90 translate-y-8"
                        >
                            <div className="inline-block bg-white/95 shadow-2xl backdrop-blur-xl my-8 p-6 sm:p-8 border border-white/30 rounded-3xl w-full max-w-4xl text-left transform transition-all overflow-hidden align-middle">
                                {/* Modal Header */}
                                <div className="flex justify-between items-center mb-8 pb-6 border-gray-200/50 border-b">
                                    <div className="flex items-center gap-4">
                                        <div className="bg-primary p-3 rounded-2xl">
                                            <FaCrown className="w-6 h-6 text-white" />
                                        </div>
                                        <div>
                                            <Dialog.Title as="h3" className="font-black text-3xl text-primary">
                                                Purchase Plan
                                            </Dialog.Title>
                                            <p className="mt-1 text-gray-600">Fill in your details to purchase the plan</p>
                                        </div>
                                    </div>
                                    <button
                                        onClick={() => setIsPlanPurchaseModalOpen(false)}
                                        className="bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors duration-200"
                                    >
                                        <FaMinusCircle className="w-6 h-6 text-gray-600" />
                                    </button>
                                </div>

                                {/* Plan Details */}
                                {selectedPlan && (
                                    <div className="bg-primary/5 mb-8 p-4 border border-black/10 rounded-2xl">
                                        <h4 className="mb-4 font-bold text-gray-900 text-xl">{selectedPlan.name}</h4>
                                        <div className="flex items-baseline gap-2 mb-4">
                                            <span className="font-black text-4xl text-primary">{currency_symbol}{selectedPlan.price}</span>
                                            <span className="text-gray-500 text-lg">/plan</span>
                                        </div>
                                        <p className="text-gray-600">{selectedPlan.description}</p>
                                        <div className="mt-4">
                                            <h5 className="mb-2 font-bold text-gray-900">Included Services:</h5>
                                            <div className="space-y-2">
                                                {selectedPlan.services.map((service) => (
                                                    <div key={service.id} className="flex justify-between items-center bg-white/80 p-3 rounded-xl">
                                                        <span className="text-gray-700">{service.name}</span>
                                                        <span className="font-bold text-primary">{service.allowed_count}x</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Form */}
                                <form onSubmit={handlePlanPurchase} className="space-y-6">
                                    <div className="gap-6 grid grid-cols-1 sm:grid-cols-2">
                                        {authUser ? (
                                            <>
                                                {/* Hidden fields for logged-in user */}
                                                <input type="hidden" name="user_id" value={authUser.id} />
                                                <input type="hidden" name="user_role" value={authUser.role} />
                                                <div className="col-span-2 font-semibold text-green-700">You are purchasing as: {authUser.name} ({authUser.email})</div>
                                            </>
                                        ) : (
                                            <>
                                                <div>
                                                    <label htmlFor="plan_name" className="block mb-3 font-bold text-gray-700 text-sm">
                                                        Full Name <span className="text-red-500">*</span>
                                                    </label>
                                                    <input
                                                        type="text"
                                                        id="plan_name"
                                                        value={planPurchaseData.name}
                                                        onChange={e => (setPlanPurchaseData as any)('name', e.target.value)}
                                                        className={`block w-full rounded-2xl border-0 bg-white/80 backdrop-blur-sm shadow-lg focus:ring-2 focus:ring-primary/50 focus:border-transparent py-4 px-4 text-gray-900 placeholder-gray-500 ${planFormErrors.name ? 'ring-2 ring-red-500' : ''
                                                            }`}
                                                        placeholder="Enter your full name"
                                                    />
                                                    {planFormErrors.name && (
                                                        <p className="flex items-center mt-2 text-red-600 text-sm">
                                                            <span className="mr-1">⚠️</span> {planFormErrors.name}
                                                        </p>
                                                    )}
                                                </div>

                                                <div>
                                                    <label htmlFor="plan_email" className="block mb-3 font-bold text-gray-700 text-sm">
                                                        Email Address <span className="text-red-500">*</span>
                                                    </label>
                                                    <input
                                                        type="email"
                                                        id="plan_email"
                                                        value={planPurchaseData.email}
                                                        onChange={e => (setPlanPurchaseData as any)('email', e.target.value)}
                                                        className={`block w-full rounded-2xl border-0 bg-white/80 backdrop-blur-sm shadow-lg focus:ring-2 focus:ring-primary/50 focus:border-transparent py-4 px-4 text-gray-900 placeholder-gray-500 ${planFormErrors.email ? 'ring-2 ring-red-500' : ''
                                                            }`}
                                                        placeholder="<EMAIL>"
                                                    />
                                                    {planFormErrors.email && (
                                                        <p className="flex items-center mt-2 text-red-600 text-sm">
                                                            <span className="mr-1">⚠️</span> {planFormErrors.email}
                                                        </p>
                                                    )}
                                                </div>

                                                <div>
                                                    <label htmlFor="plan_phone" className="block mb-3 font-bold text-gray-700 text-sm">
                                                        Phone Number <span className="text-red-500">*</span>
                                                    </label>
                                                    <input
                                                        type="tel"
                                                        id="plan_phone"
                                                        value={planPurchaseData.phone}
                                                        onChange={e => (setPlanPurchaseData as any)('phone', e.target.value)}
                                                        className={`block w-full rounded-2xl border-0 bg-white/80 backdrop-blur-sm shadow-lg focus:ring-2 focus:ring-primary/50 focus:border-transparent py-4 px-4 text-gray-900 placeholder-gray-500 ${planFormErrors.phone ? 'ring-2 ring-red-500' : ''
                                                            }`}
                                                        placeholder="+****************"
                                                    />
                                                    {planFormErrors.phone && (
                                                        <p className="flex items-center mt-2 text-red-600 text-sm">
                                                            <span className="mr-1">⚠️</span> {planFormErrors.phone}
                                                        </p>
                                                    )}
                                                </div>

                                                <div>
                                                    <label htmlFor="plan_password" className="block mb-3 font-bold text-gray-700 text-sm">
                                                        Password <span className="text-gray-500 text-sm">(for new accounts)</span>
                                                    </label>
                                                    <input
                                                        type="password"
                                                        id="plan_password"
                                                        value={planPurchaseData.password}
                                                        onChange={e => (setPlanPurchaseData as any)('password', e.target.value)}
                                                        className={`block w-full rounded-2xl border-0 bg-white/80 backdrop-blur-sm shadow-lg focus:ring-2 focus:ring-primary/50 focus:border-transparent py-4 px-4 text-gray-900 placeholder-gray-500 ${planFormErrors.password ? 'ring-2 ring-red-500' : ''
                                                            }`}
                                                        placeholder="Create a secure password"
                                                    />
                                                    {planFormErrors.password && (
                                                        <p className="flex items-center mt-2 text-red-600 text-sm">
                                                            <span className="mr-1">⚠️</span> {planFormErrors.password}
                                                        </p>
                                                    )}
                                                </div>
                                                {!authUser && (
                                                    <>
                                                        <div>
                                                            <label htmlFor="plan_anniversary" className="block mb-3 font-bold text-gray-700 text-sm">
                                                                Anniversary (optional)
                                                            </label>
                                                            <input
                                                                type="date"
                                                                id="plan_anniversary"
                                                                value={planPurchaseData.anniversary ? String(planPurchaseData.anniversary).slice(0, 10) : ''}
                                                                onChange={e => (setPlanPurchaseData as any)('anniversary', e.target.value)}
                                                                className="block bg-white/80 shadow-lg backdrop-blur-sm px-4 py-4 border-0 focus:border-transparent rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900 placeholder-gray-500"
                                                                placeholder="Anniversary date"
                                                            />
                                                        </div>
                                                        <div>
                                                            <label htmlFor="plan_date_of_birth" className="block mb-3 font-bold text-gray-700 text-sm">
                                                                Date of Birth (optional)
                                                            </label>
                                                            <input
                                                                type="date"
                                                                id="plan_date_of_birth"
                                                                value={planPurchaseData.date_of_birth ? String(planPurchaseData.date_of_birth).slice(0, 10) : ''}
                                                                onChange={e => (setPlanPurchaseData as any)('date_of_birth', e.target.value)}
                                                                className="block bg-white/80 shadow-lg backdrop-blur-sm px-4 py-4 border-0 focus:border-transparent rounded-2xl focus:ring-2 focus:ring-primary/50 w-full text-gray-900 placeholder-gray-500"
                                                                placeholder="Date of birth"
                                                            />
                                                        </div>
                                                    </>
                                                )}
                                            </>
                                        )}
                                    </div>

                                    {/* Required Fields Note */}
                                    {!authUser && (
                                        <div className="flex items-center text-gray-500 text-sm">
                                            <span className="mr-1 text-red-500">*</span>
                                            <span>Required fields</span>
                                        </div>
                                    )}

                                    {/* General Error Message */}
                                    {planFormErrors.error && (
                                        <div className="bg-red-50 px-4 py-3 border border-red-200 rounded-xl text-red-600">
                                            <p className="flex items-center">
                                                <span className="mr-2">⚠️</span>
                                                {planFormErrors.error}
                                            </p>
                                        </div>
                                    )}

                                    <div className="flex sm:flex-row flex-col gap-4 pt-8">
                                        <button
                                            type="button"
                                            onClick={() => setIsPlanPurchaseModalOpen(false)}
                                            className="flex-1 sm:flex-none bg-white/80 hover:bg-gray-50 backdrop-blur-sm px-8 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-gray-200/50 font-bold text-gray-700 text-lg transform transition-all duration-300 focus:outline-none hover:scale-105"
                                        >
                                            Cancel
                                        </button>
                                        <button
                                            type="submit"
                                            disabled={processing}
                                            className="group relative flex-1 bg-primary hover:bg-black disabled:opacity-50 hover:shadow-2xl px-8 py-4 rounded-2xl focus:ring-4 focus:ring-primary/30 font-bold text-lg text-white transform transition-all duration-500 disabled:cursor-not-allowed overflow-hidden focus:outline-none hover:scale-105 disabled:hover:scale-100"
                                        >
                                            <div className="relative flex justify-center items-center gap-3">
                                                {processing ? (
                                                    <>
                                                        <div className="border-white border-b-2 rounded-full w-5 h-5 animate-spin"></div>
                                                        <span>Processing...</span>
                                                    </>
                                                ) : (
                                                    <>
                                                        <FaCrown className="w-5 h-5" />
                                                        <span>Purchase Plan</span>
                                                        <FaArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1 duration-300" />
                                                    </>
                                                )}
                                            </div>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </Transition.Child>
                    </div>
                </Dialog>
            </Transition>

            {/* Success Modal */}
            <Transition appear show={isSuccessModalOpen} as={Fragment}>
                <Dialog
                    as="div"
                    className="z-50 fixed inset-0 overflow-y-auto"
                    onClose={() => setIsSuccessModalOpen(false)}
                >
                    <div className="px-4 min-h-screen text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-500"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="ease-in duration-300"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <div className="fixed inset-0 bg-black/60 backdrop-blur-md" />
                        </Transition.Child>

                        <span className="inline-block h-screen align-middle" aria-hidden="true">
                            &#8203;
                        </span>

                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-500"
                            enterFrom="opacity-0 scale-90 translate-y-8"
                            enterTo="opacity-100 scale-100 translate-y-0"
                            leave="ease-in duration-300"
                            leaveFrom="opacity-100 scale-100 translate-y-0"
                            leaveTo="opacity-0 scale-90 translate-y-8"
                        >
                            <div className="inline-block bg-white/95 shadow-2xl backdrop-blur-xl my-8 p-6 sm:p-8 border border-white/30 rounded-3xl w-full max-w-md text-left transform transition-all overflow-hidden align-middle">
                                <div className="text-center">
                                    <div className="flex justify-center items-center bg-green-100 mx-auto mb-4 rounded-full w-16 h-16">
                                        <FaCheck className="w-8 h-8 text-green-600" />
                                    </div>
                                    <Dialog.Title as="h3" className="mb-4 font-bold text-2xl text-gray-900">
                                        Success!
                                    </Dialog.Title>
                                    <p className="mb-8 text-gray-600">
                                        {successMessage}
                                    </p>
                                    <button
                                        type="button"
                                        onClick={() => setIsSuccessModalOpen(false)}
                                        className="inline-flex justify-center bg-primary hover:bg-black hover:shadow-xl px-6 py-3 rounded-2xl focus:ring-4 focus:ring-primary/30 font-medium text-base text-white transition-all duration-300 focus:outline-none"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        </Transition.Child>
                    </div>
                </Dialog>
            </Transition>
            {/* Footer  Section */}
            <div className='max-md:mb-20 px-4 py-6 border-t border-black/10 text-center'>
                <p className="text-base text-gray-500 dark:text-white">© {new Date().getFullYear()} Salozy - All Rights Reserved.</p>
            </div>
            {/* Mobile Sticky Bottom Bar */}
            <div className="right-0 bottom-0 left-0 z-40 fixed md:hidden bg-white/95 shadow-[0_-4px_20px_rgba(0,0,0,0.1)] backdrop-blur-xl border-white/30 border-t">
                <div className="flex justify-between items-center px-4 py-3">
                    <a href={`tel:${tenant.phone}`} className="flex flex-col items-center">
                        <div className="bg-green-500 p-2 rounded-full">
                            <FaPhone className="w-4 h-4 text-white" />
                        </div>
                        <span className="mt-1 font-medium text-xs">Call</span>
                    </a>

                    <a href={`mailto:${tenant.email}`} className="flex flex-col items-center">
                        <div className="bg-blue-500 p-2 rounded-full">
                            <FaEnvelope className="w-4 h-4 text-white" />
                        </div>
                        <span className="mt-1 font-medium text-xs">Email</span>
                    </a>

                    <button
                        onClick={() => setShowCart(true)}
                        className="relative flex flex-col items-center"
                    >
                        <div className="bg-primary p-2 rounded-full transition-transform duration-200 mobile-cart-icon">
                            <FaShoppingCart className="w-4 h-4 text-white" />
                        </div>
                        {selectedServices.length > 0 && (
                            <span className="-top-1 -right-1 absolute flex justify-center items-center bg-red-500 rounded-full w-5 h-5 text-white text-xs">
                                {selectedServices.length}
                            </span>
                        )}
                        <span className="mt-1 font-medium text-xs">Cart</span>
                    </button>

                    <button
                        onClick={() => setIsBookingModalOpen(true)}
                        className="flex flex-col items-center"
                    >
                        <div className="bg-black p-2 rounded-full">
                            <FaCalendarAlt className="w-4 h-4 text-white" />
                        </div>
                        <span className="mt-1 font-medium text-xs">Book</span>
                    </button>
                </div>
            </div>

        </div>
    );
};

FrontPage.layout = 'blank';

export default FrontPage;
