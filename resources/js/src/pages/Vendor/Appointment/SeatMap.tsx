import React, { useState } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import Swal from 'sweetalert2';
import 'sweetalert2/dist/sweetalert2.min.css';
import { setPageTitle } from '@store/themeConfigSlice';

interface User {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    status: string;
    seat_id?: number;
    seat_name?: string;
}

interface Appointment {
    id: number;
    service_id: number;
    service_name: string;
    status: string;
    user: User;
    ticket_number: string;
    start_time?: string;
    estimated_end_time?: string;
    appointment_date?: string;
    appointment_time?: string;
}

interface UnassignedAppointment {
    id: number;
    ticket_number: string;
    status: string;
    user: User;
    services: Service[];
    appointment_date?: string;
    appointment_time?: string;
    start_time?: string;
    estimated_end_time?: string;
}

interface Seat {
    id: number;
    name: string;
    status: 'available' | 'occupied' | 'cleaning' | 'maintenance';
    staff: {
        id: number;
        name: string;
    } | null;
    appointments: Appointment[];
}

interface SeatAvailabilityResponse {
    available: boolean;
}

interface Props {
    seats: Seat[];
    unassignedAppointments: UnassignedAppointment[];
    assignedPendingAppointments: UnassignedAppointment[];
}

// Utility functions
const formatDateTime = (date?: string, time?: string) => {
    if (!date || !time) return 'N/A';
    return `${date} ${time}`;
};

const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
};

const SeatMap = ({ seats, unassignedAppointments, assignedPendingAppointments }: Props) => {
    const dispatch = useDispatch();
    const [selectedSeat, setSelectedSeat] = useState<Seat | null>(null);
    const [draggedAppointment, setDraggedAppointment] = useState<UnassignedAppointment | null>(null);
    const [draggedSeatAppointment, setDraggedSeatAppointment] = useState<Appointment | null>(null);

    const props = usePage().props as any;
    const user = props.auth.user;
    const roles = user?.roles || [];
    const isVendor = roles.some((role: any) => role.name && ['vendor'].includes(role.name.toLowerCase()));
    const isAdmin = roles.some((role: any) => role.name === 'admin');
    const isBranchuser = roles.some((role: any) => role.name && ['branchuser'].includes(role.name.toLowerCase()));
    const isStaff = roles.some((role: any) => role.name === 'staff');

    React.useEffect(() => {
        dispatch(setPageTitle('Seat Map'));
    }, [dispatch]);

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'available':
                return <span className="badge badge-outline-success">Available</span>;
            case 'occupied':
                return <span className="badge badge-outline-warning">Occupied</span>;
            case 'cleaning':
                return <span className="badge badge-outline-info">Cleaning</span>;
            case 'maintenance':
                return <span className="badge badge-outline-danger">Maintenance</span>;
            default:
                return <span className="badge badge-outline-secondary">{status}</span>;
        }
    };

    const formatTime = (time: string) => {
        return new Date(time).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    const handleAssignSeat = (appointment: UnassignedAppointment, seat: Seat) => {
        Swal.fire({
            title: 'Assign Appointment to Seat',
            html: `
                <div class="text-left space-y-4">
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">Appointment Details</h3>
                        <div class="space-y-2">
                            <p class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300"><strong>Customer:</strong> ${appointment.user.name}</span>
                            </p>
                            <p class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300"><strong>Ticket:</strong> ${appointment.ticket_number}</span>
                            </p>
                            <p class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300"><strong>Date & Time:</strong> ${formatDateTime(appointment.appointment_date, appointment.appointment_time)}</span>
                            </p>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">Services</h3>
                        <div class="space-y-2 max-h-40 overflow-y-auto">
                            ${appointment.services.map(service => `
                                <div class="flex items-center justify-between p-2 bg-white dark:bg-gray-700 rounded">
                                    <span class="text-gray-700 dark:text-gray-300">${service.name}</span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">${formatDuration(service.duration_minutes)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">Assignment Details</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Seat</label>
                                <div class="flex items-center gap-2 p-2 bg-white dark:bg-gray-700 rounded">
                                    <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                    </svg>
                                    <span class="text-gray-700 dark:text-gray-300">${seat.name}</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                                <select id="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Assign',
            cancelButtonText: 'Cancel',
            showLoaderOnConfirm: true,
            customClass: {
                popup: 'dark:bg-gray-900',
                title: 'text-gray-900 dark:text-gray-100',
                htmlContainer: 'text-gray-700 dark:text-gray-300',
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger'
            },
            preConfirm: () => {
                const status = (document.getElementById('status') as HTMLSelectElement).value;
                return { status };
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.isConfirmed) {
                const { status } = result.value;

                // Prepare the data for the update
                const updateData = {
                    user_id: appointment.user.id,
                    appointment_date: appointment.appointment_date,
                    appointment_time: appointment.appointment_time,
                    services: appointment.services.map(service => ({ id: service.id })),
                    seat_id: seat.id,
                    status: status,
                    notes: ''
                };

                // Send the update request
                router.put(`/vendor/appointments/${appointment.id}`, updateData, {
                    onSuccess: () => {
                        Swal.fire({
                            title: 'Success!',
                            text: 'Appointment assigned successfully',
                            icon: 'success',
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300',
                                confirmButton: 'btn btn-primary'
                            }
                        }).then(() => {
                            router.reload();
                        });
                    },
                    onError: () => {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to assign appointment',
                            icon: 'error',
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300',
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            }
        });
    };

    const handleUpdateStatus = (appointment: Appointment, seat: Seat) => {
        console.log('Appointment data:', appointment); // Debug log
        Swal.fire({
            title: 'Update Service Status',
            html: `
                <div class="text-left space-y-4">
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">Service Details</h3>
                        <div class="space-y-2">
                            <p class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300"><strong>Customer:</strong> ${appointment.user.name}</span>
                            </p>
                            <p class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300"><strong>Ticket:</strong> ${appointment.ticket_number}</span>
                            </p>
                            <p class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                <span class="text-gray-700 dark:text-gray-300"><strong>Service:</strong> ${appointment.service_name}</span>
                            </p>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">Update Status</h3>
                        <select id="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
                            <option value="pending" ${appointment.status === 'pending' ? 'selected' : ''}>Pending</option>
                            <option value="in_progress" ${appointment.status === 'in_progress' ? 'selected' : ''}>In Progress</option>
                            <option value="completed" ${appointment.status === 'completed' ? 'selected' : ''}>Completed</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Update',
            cancelButtonText: 'Cancel',
            showLoaderOnConfirm: true,
            customClass: {
                popup: 'dark:bg-gray-900',
                title: 'text-gray-900 dark:text-gray-100',
                htmlContainer: 'text-gray-700 dark:text-gray-300',
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger'
            },
            preConfirm: () => {
                const status = (document.getElementById('status') as HTMLSelectElement).value;
                return { status };
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.isConfirmed) {
                const { status } = result.value;

                // Send the update request with service_id
                router.put(`/vendor/appointments/${appointment.id}/update-status`, {
                    status,
                    service_id: appointment.service_id
                }, {
                    onSuccess: () => {
                        Swal.fire({
                            title: 'Success!',
                            text: 'Service status updated successfully',
                            icon: 'success',
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300',
                                confirmButton: 'btn btn-primary'
                            }
                        }).then(() => {
                            router.reload();
                        });
                    },
                    onError: (errors) => {
                        console.error('Error data:', errors); // Debug log
                        Swal.fire({
                            title: 'Error!',
                            text: errors?.message || 'Failed to update service status',
                            icon: 'error',
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300',
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            }
        });
    };

    const handleDragStart = (appointment: UnassignedAppointment) => {
        setDraggedAppointment(appointment);
    };

    const handleDragEnd = () => {
        setDraggedAppointment(null);
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
    };

    const handleSeatDragStart = (appointment: Appointment, seat: Seat) => {
        // Check if all services are in pending status
        const allServicesPending = seat.appointments.every(service => service.status === 'pending');
        // if (!allServicesPending) {
        //     Swal.fire({
        //         title: 'Cannot Move',
        //         text: 'All services must be in pending status to move the appointment.',
        //         icon: 'warning',
        //         customClass: {
        //             popup: 'dark:bg-gray-900',
        //             title: 'text-gray-900 dark:text-gray-100',
        //             htmlContainer: 'text-gray-700 dark:text-gray-300',
        //             confirmButton: 'btn btn-primary'
        //         }
        //     });
        //     return;
        // }
        setDraggedSeatAppointment(appointment);
    };

    const handleSeatDragEnd = () => {
        setDraggedSeatAppointment(null);
    };

    const handleDrop = async (seat: Seat) => {
        // Handle drop from Assigned Pending Services
        if (draggedAppointment) {
            if (seat.status !== 'available') {
                const toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    customClass: {
                        popup: 'dark:bg-gray-900',
                        title: 'text-gray-900 dark:text-gray-100',
                        htmlContainer: 'text-gray-700 dark:text-gray-300'
                    }
                });

                toast.fire({
                    icon: 'error',
                    title: 'This seat is not available at the moment.'
                });
                return;
            }

            try {
                const response = await fetch(`/vendor/appointments/seats/${seat.id}/check-availability`);
                const data = await response.json();

                if (!data.available) {
                    const toast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        customClass: {
                            popup: 'dark:bg-gray-900',
                            title: 'text-gray-900 dark:text-gray-100',
                            htmlContainer: 'text-gray-700 dark:text-gray-300'
                        }
                    });

                    toast.fire({
                        icon: 'error',
                        title: 'This seat is not available at the moment.'
                    });
                    return;
                }

                // Update appointment status to in_progress
                router.put(`/vendor/appointments/${draggedAppointment.id}/update-seat-status`, {
                    status: 'in_progress',
                    seat_id: seat.id
                }, {
                    preserveScroll: true,
                    onSuccess: () => {
                        const toast = Swal.mixin({
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300'
                            }
                        });

                        toast.fire({
                            icon: 'success',
                            title: 'Appointment assigned to seat successfully'
                        });
                    },
                    onError: (errors) => {
                        const toast = Swal.mixin({
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300'
                            }
                        });

                        toast.fire({
                            icon: 'error',
                            title: errors?.message || 'Failed to assign appointment to seat'
                        });
                    }
                });
            } catch (error) {
                console.error('Error checking seat availability:', error);
                const toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    customClass: {
                        popup: 'dark:bg-gray-900',
                        title: 'text-gray-900 dark:text-gray-100',
                        htmlContainer: 'text-gray-700 dark:text-gray-300'
                    }
                });

                toast.fire({
                    icon: 'error',
                    title: 'Failed to check seat availability'
                });
            }
        }
        // Handle drop from another seat
        else if (draggedSeatAppointment) {
            if (seat.status !== 'available') {
                const toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    customClass: {
                        popup: 'dark:bg-gray-900',
                        title: 'text-gray-900 dark:text-gray-100',
                        htmlContainer: 'text-gray-700 dark:text-gray-300'
                    }
                });

                toast.fire({
                    icon: 'error',
                    title: 'This seat is not available at the moment.'
                });
                return;
            }

            try {
                const response = await fetch(`/vendor/appointments/seats/${seat.id}/check-availability`);
                const data = await response.json();

                if (!data.available) {
                    const toast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        customClass: {
                            popup: 'dark:bg-gray-900',
                            title: 'text-gray-900 dark:text-gray-100',
                            htmlContainer: 'text-gray-700 dark:text-gray-300'
                        }
                    });

                    toast.fire({
                        icon: 'error',
                        title: 'This seat is not available at the moment.'
                    });
                    return;
                }

                // Update appointment status to in_progress with new seat
                router.put(`/vendor/appointments/${draggedSeatAppointment.id}/update-seat-status`, {
                    status: 'in_progress',
                    seat_id: seat.id
                }, {
                    preserveScroll: true,
                    onSuccess: () => {
                        const toast = Swal.mixin({
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300'
                            }
                        });

                        toast.fire({
                            icon: 'success',
                            title: 'Appointment moved to new seat successfully'
                        });
                    },
                    onError: (errors) => {
                        const toast = Swal.mixin({
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300'
                            }
                        });

                        toast.fire({
                            icon: 'error',
                            title: errors?.message || 'Failed to move appointment to new seat'
                        });
                    }
                });
            } catch (error) {
                console.error('Error checking seat availability:', error);
                const toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    customClass: {
                        popup: 'dark:bg-gray-900',
                        title: 'text-gray-900 dark:text-gray-100',
                        htmlContainer: 'text-gray-700 dark:text-gray-300'
                    }
                });

                toast.fire({
                    icon: 'error',
                    title: 'Failed to check seat availability'
                });
            }
        }
    };

    const handlePendingDrop = async () => {
        if (!draggedSeatAppointment) return;

        // Find the seat that contains this appointment
        const seat = seats.find(s =>
            s.appointments.some(a => a.id === draggedSeatAppointment.id)
        );

        if (!seat) return;

        // Update appointment status to pending
        router.put(`/vendor/appointments/${draggedSeatAppointment.id}/update-seat-status`, {
            status: 'pending',
            seat_id: seat.id // Get seat_id from the seat object
        }, {
            preserveScroll: true,
            onSuccess: () => {
                const toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    customClass: {
                        popup: 'dark:bg-gray-900',
                        title: 'text-gray-900 dark:text-gray-100',
                        htmlContainer: 'text-gray-700 dark:text-gray-300'
                    }
                });

                toast.fire({
                    icon: 'success',
                    title: 'Appointment moved to pending successfully'
                });
            },
            onError: (errors) => {
                const toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    customClass: {
                        popup: 'dark:bg-gray-900',
                        title: 'text-gray-900 dark:text-gray-100',
                        htmlContainer: 'text-gray-700 dark:text-gray-300'
                    }
                });

                toast.fire({
                    icon: 'error',
                    title: errors?.message || 'Failed to move appointment to pending'
                });
            }
        });
    };

    const handleOverallStatusUpdate = (appointment: Appointment, seat: Seat, status: 'completed' | 'cancelled') => {
        Swal.fire({
            title: `Mark Appointment as ${status.charAt(0).toUpperCase() + status.slice(1)}`,
            text: `Are you sure you want to mark this appointment as ${status}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: `Yes, mark as ${status}`,
            cancelButtonText: 'Cancel',
            customClass: {
                popup: 'dark:bg-gray-900',
                title: 'text-gray-900 dark:text-gray-100',
                htmlContainer: 'text-gray-700 dark:text-gray-300',
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                router.put(`/vendor/appointments/${appointment.id}/update-seat-status`, {
                    status: status,
                    seat_id: seat.id
                }, {
                    onSuccess: () => {
                        Swal.fire({
                            title: 'Success!',
                            text: `Appointment marked as ${status} successfully`,
                            icon: 'success',
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300',
                                confirmButton: 'btn btn-primary'
                            }
                        }).then(() => {
                            router.reload();
                        });
                    },
                    onError: (errors) => {
                        Swal.fire({
                            title: 'Error!',
                            text: errors?.message || `Failed to mark appointment as ${status}`,
                            icon: 'error',
                            customClass: {
                                popup: 'dark:bg-gray-900',
                                title: 'text-gray-900 dark:text-gray-100',
                                htmlContainer: 'text-gray-700 dark:text-gray-300',
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            }
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Appointments</span>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Seat Map</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex md:items-center md:flex-row flex-col mb-5 gap-5">
                        <div className="flex items-center gap-5 flex-1">
                            <h2 className="text-xl">Seat Map View</h2>
                        </div>
                        {!isStaff  && (
                        <div className="flex items-center gap-5">
                            <Link href="/vendor/appointments" className="btn btn-primary">
                                <svg className="w-5 h-5 ltr:mr-2 rtl:ml-2" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                    <line x1="19" y1="12" x2="5" y2="12"></line>
                                    <polyline points="12 19 5 12 12 5"></polyline>
                                </svg>
                                Back to Appointments
                            </Link>
                        </div>
                        )}
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Seat Map Section */}
                        <div className="lg:col-span-2">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                                {seats.map((seat) => (
                                    <div
                                        key={seat.id}
                                        className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-lg ${
                                            seat.status === 'available'
                                                ? 'bg-success/10 border-success hover:bg-success/20'
                                                : seat.status === 'occupied'
                                                ? 'bg-warning/10 border-warning'
                                                : seat.status === 'cleaning'
                                                ? 'bg-info/10 border-info'
                                                : 'bg-danger/10 border-danger'
                                        }`}
                                        onDragOver={handleDragOver}
                                        onDrop={() => handleDrop(seat)}
                                    >
                                        <div className="flex justify-between items-start mb-2">
                                            <h3 className="font-semibold">{seat.name}</h3>
                                            {getStatusBadge(seat.status)}
                                        </div>
                                        {seat.staff && (
                                            <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                                Staff: {seat.staff.name}
                                            </div>
                                        )}
                                        {seat.appointments.length > 0 && (
                                            <div className="mt-2 space-y-3">
                                                <div
                                                    className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 cursor-move"
                                                    draggable
                                                    onDragStart={() => handleSeatDragStart(seat.appointments[0], seat)}
                                                    onDragEnd={handleSeatDragEnd}
                                                >
                                                    <div className="flex items-center gap-3">
                                                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                                                            <span className="text-primary text-lg font-semibold">
                                                                {seat.appointments[0].user.name.charAt(0)}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <div className="font-semibold text-gray-900 dark:text-gray-100">
                                                                {seat.appointments[0].user.name}
                                                            </div>
                                                            <div className="text-xs text-gray-500 dark:text-gray-400">
                                                                {seat.appointments[0].user.email}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="space-y-2">
                                                    {seat.appointments.map((appointment) => (
                                                        <div
                                                            key={`${appointment.id}-${appointment.service_id}`}
                                                            className="text-sm bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200 dark:border-gray-700"
                                                        >
                                                            <div className="space-y-2">
                                                                <div className="flex items-center justify-between">
                                                                    <div className="text-gray-900 dark:text-gray-100 font-medium">
                                                                        {appointment.service_name}
                                                                    </div>
                                                                </div>
                                                                {(appointment.start_time || appointment.estimated_end_time) && (
                                                                    <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                                                                        {appointment.start_time && (
                                                                            <div className="flex items-center gap-1">
                                                                                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                                </svg>
                                                                                Start: {formatTime(appointment.start_time)}
                                                                            </div>
                                                                        )}
                                                                        {appointment.estimated_end_time && (
                                                                            <div className="flex items-center gap-1">
                                                                                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                                </svg>
                                                                                Est. End: {formatTime(appointment.estimated_end_time)}
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    ))}
                                                    <div className="flex gap-2 pt-2">
                                                        <button
                                                            className="btn btn-success btn-sm"
                                                            onClick={() => handleOverallStatusUpdate(seat.appointments[0], seat, 'completed')}
                                                        >
                                                            Mark as Completed
                                                        </button>
                                                        <button
                                                            className="btn btn-danger btn-sm"
                                                            onClick={() => handleOverallStatusUpdate(seat.appointments[0], seat, 'cancelled')}
                                                        >
                                                            Mark as Cancelled
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Appointments Section */}
                        <div className="lg:col-span-1">
                            <div className="space-y-6">

                                {/* Unassigned Appointments */}
                                {!isStaff  && (
                                <div className="bg-white dark:bg-gray-800 rounded-lg border p-4">
                                    <h3 className="text-lg font-semibold mb-4">Pending to Approve Appoinment</h3>
                                    <div className="space-y-4 max-h-[300px] overflow-y-auto">
                                        {unassignedAppointments.length === 0 ? (
                                            <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                                                No unassigned appointments
                                            </div>
                                        ) : (
                                            unassignedAppointments.map((appointment) => (
                                                <AppointmentCard key={appointment.id} appointment={appointment} />
                                            ))
                                        )}
                                    </div>
                                </div>
                                )}

                                {/* Assigned Pending Appointments */}
                                <div
                                    className="bg-white dark:bg-gray-800 rounded-lg border p-4"
                                    onDragOver={handleDragOver}
                                    onDrop={handlePendingDrop}
                                >
                                    <h3 className="text-lg font-semibold mb-4"> Approved Appoinment</h3>
                                    <div className="space-y-4 max-h-[300px] overflow-y-auto">
                                        {assignedPendingAppointments.length === 0 ? (
                                            <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                                                No assigned pending services
                                            </div>
                                        ) : (
                                            assignedPendingAppointments.map((appointment) => (
                                                <div
                                                    key={appointment.id}
                                                    className="border rounded-lg p-4 bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-200 cursor-move"
                                                    draggable
                                                    onDragStart={() => handleDragStart(appointment)}
                                                    onDragEnd={handleDragEnd}
                                                >
                                                    <div className="flex justify-between items-start mb-3">
                                                        <div className="flex items-center gap-3">
                                                            <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                                                                <span className="text-primary font-semibold">
                                                                    {appointment.user.name.charAt(0)}
                                                                </span>
                                                            </div>
                                                            <div>
                                                                <div className="font-medium text-gray-900 dark:text-gray-100">
                                                                    {appointment.user.name}
                                                                </div>
                                                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                                                    {appointment.ticket_number}
                                                                </div>
                                                                {appointment.services[0]?.seat_name && (
                                                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                                                        Seat: {appointment.services[0].seat_name}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                        {!isStaff  && ( <Link href={`/vendor/appointments/${appointment.id}/edit`} className="btn btn-sm btn-outline-primary"> Edit </Link> )}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <div className="flex flex-wrap gap-2">
                                                            {appointment.services.map((service) => (
                                                                <span key={service.id} className="badge badge-outline-primary">
                                                                    <span className="flex items-center gap-1">
                                                                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                        </svg>
                                                                        {service.name} ({formatDuration(service.duration_minutes)})
                                                                    </span>
                                                                </span>
                                                            ))}
                                                        </div>
                                                    </div>
                                                </div>
                                            ))
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

// Appointment Card Component
const AppointmentCard = ({ appointment }: { appointment: UnassignedAppointment }) => (
    <div className="border rounded-lg p-4 bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-200">
        <div className="flex justify-between items-start mb-3">
            <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="text-primary font-semibold">
                        {appointment.user.name.charAt(0)}
                    </span>
                </div>
                <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                        {appointment.user.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                        {appointment.ticket_number}
                    </div>
                </div>
            </div>
            <div className="flex gap-2">
                {/* <span className={`badge ${
                    appointment.status === 'pending' ? 'badge-outline-warning' : 'badge-outline-info'
                }`}>
                    {appointment.status}
                </span> */}
                <Link
                    href={`/vendor/appointments/${appointment.id}/edit`}
                    className="badge badge-outline-primary"
                >
                    Edit
                </Link>
            </div>
        </div>

        <div className="space-y-2 mb-3">
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                {appointment.user.phone}
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                {appointment.user.email}
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {formatDateTime(appointment.appointment_date, appointment.appointment_time)}
            </div>
        </div>

        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <div className="flex flex-wrap gap-2">
                {appointment.services.map((service) => (
                    <span key={service.id} className="badge badge-outline-primary">
                        <span className="flex items-center gap-1">
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {service.name} ({formatDuration(service.duration_minutes)})
                        </span>
                    </span>
                ))}
            </div>
        </div>
    </div>
);

export default SeatMap;
