import React, { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@store/themeConfigSlice';
import Pagination from '@components/Pagination';

interface Service {
    id: number;
    name: string;
    price: number;
}

interface Appointment {
    id: number;
    appointment_date: string;
    appointment_time: string;
    ticket_number: string;
    status: string;
    notes: string;
    services: Service[];
    branch: {
        id: number;
        name: string;
    };
}

interface Customer {
    id: number;
    name: string;
    email: string;
}

interface PaginationData {
    data: Appointment[];
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

interface Props {
    customer: Customer;
    appointments: PaginationData;
    filters: {
        search: string;
        status: string;
        date_from: string;
        date_to: string;
    };
}

const BookingHistory: React.FC<Props> = ({ customer, appointments, filters }) => {
    const dispatch = useDispatch();
    const [search, setSearch] = useState(filters.search);
    const [status, setStatus] = useState(filters.status);
    const [dateFrom, setDateFrom] = useState(filters.date_from);
    const [dateTo, setDateTo] = useState(filters.date_to);

    useEffect(() => {
        dispatch(setPageTitle(`Booking History - ${customer.name}`));
    }, [dispatch, customer.name]);

    const handleSearch = (value: string) => {
        setSearch(value);
        router.get(
            `/vendor/manage-customer/${customer.id}/booking-history`,
            {
                search: value,
                status,
                date_from: dateFrom,
                date_to: dateTo
            },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleStatusChange = (value: string) => {
        setStatus(value);
        router.get(
            `/vendor/manage-customer/${customer.id}/booking-history`,
            {
                search,
                status: value,
                date_from: dateFrom,
                date_to: dateTo
            },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleDateFromChange = (value: string) => {
        setDateFrom(value);
        router.get(
            `/vendor/manage-customer/${customer.id}/booking-history`,
            {
                search,
                status,
                date_from: value,
                date_to: dateTo
            },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleDateToChange = (value: string) => {
        setDateTo(value);
        router.get(
            `/vendor/manage-customer/${customer.id}/booking-history`,
            {
                search,
                status,
                date_from: dateFrom,
                date_to: value
            },
            { preserveState: true, preserveScroll: true }
        );
    };

    const getStatusBadgeClass = (status: string) => {
        switch (status) {
            case 'completed':
                return 'badge badge-outline-success';
            case 'pending':
                return 'badge badge-outline-warning';
            case 'cancelled':
                return 'badge badge-outline-danger';
            case 'in_progress':
                return 'badge badge-outline-info';
            default:
                return 'badge badge-outline-secondary';
        }
    };

    const getTotalPrice = (services: Service[]) => {
        return services.reduce((total, service) => total + service.price, 0);
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/manage-customer" className="text-primary hover:underline">
                        Customers
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={`/vendor/manage-customer/show/${customer.id}`} className="text-primary hover:underline">
                        {customer.name}
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Booking History</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">
                            Booking History - {customer.name}
                        </h5>
                        <div className="text-sm text-gray-500">
                            Total: {appointments.total} appointments
                        </div>
                    </div>

                    {/* Filters */}
                    <div className="flex md:items-center md:flex-row flex-col mb-5 gap-5">
                        <div className="flex items-center gap-5 flex-1">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search tickets or notes..."
                                    className="form-input py-2 ltr:pr-11 rtl:pl-11 peer"
                                    value={search}
                                    onChange={(e) => handleSearch(e.target.value)}
                                />
                                <button type="button" className="absolute ltr:right-[11px] rtl:left-[11px] top-1/2 -translate-y-1/2 peer-focus:text-primary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="11.5" cy="11.5" r="9.5" stroke="currentColor" strokeWidth="1.5" opacity="0.5"></circle>
                                        <path d="M18.5 18.5L22 22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"></path>
                                    </svg>
                                </button>
                            </div>

                            <select
                                className="form-select"
                                value={status}
                                onChange={(e) => handleStatusChange(e.target.value)}
                            >
                                <option value="">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>

                            <input
                                type="date"
                                className="form-input"
                                value={dateFrom}
                                onChange={(e) => handleDateFromChange(e.target.value)}
                                placeholder="From Date"
                            />

                            <input
                                type="date"
                                className="form-input"
                                value={dateTo}
                                onChange={(e) => handleDateToChange(e.target.value)}
                                placeholder="To Date"
                            />
                        </div>
                    </div>

                    {/* Appointments Table */}
                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Ticket Number</th>
                                    <th>Services</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {appointments.data.map((appointment) => (
                                    <tr key={appointment.id}>
                                        <td>
                                            <div className="font-semibold">
                                                {new Date(appointment.appointment_date).toLocaleDateString()}
                                            </div>
                                        </td>
                                        <td>{appointment.appointment_time}</td>
                                        <td>
                                            <span className="font-mono text-sm bg-gray-100 dark:bg-dark px-2 py-1 rounded">
                                                {appointment.ticket_number}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="space-y-1">
                                                {appointment.services.map((service) => (
                                                    <div key={service.id} className="text-sm">
                                                        • {service.name} - ${service.price}
                                                    </div>
                                                ))}
                                            </div>
                                        </td>
                                        <td>
                                            <span className="font-semibold text-primary">
                                                ${getTotalPrice(appointment.services)}
                                            </span>
                                        </td>
                                        <td>
                                            <span className={getStatusBadgeClass(appointment.status)}>
                                                {appointment.status.replace('_', ' ')}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="text-sm text-gray-500 max-w-xs truncate block">
                                                {appointment.notes || 'No notes'}
                                            </span>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {appointments.data.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                            No appointments found matching your criteria.
                        </div>
                    )}

                    {/* Pagination */}
                    <div className="mt-6">
                        <Pagination links={appointments.links} />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default BookingHistory;
