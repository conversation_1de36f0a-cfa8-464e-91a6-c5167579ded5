import React, { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@store/themeConfigSlice';
import Pagination from '@components/Pagination';
import AddCustomerModal from '@components/AddCustomerModal';

interface Customer {
    id: number;
    name: string;
    email: string;
    phone: string;
    gender: string;
    company_name: string;
    is_active: boolean;
    appointments_count: number;
    created_at: string;
}

interface PaginationData {
    data: Customer[];
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

interface Props {
    customers: PaginationData;
    filters: {
        search: string;
        status: string;
        gender: string;
        sort: string;
        direction: string;
    };
}

const Index: React.FC<Props> = ({ customers, filters }) => {
    const dispatch = useDispatch();
    const [search, setSearch] = useState(filters.search);
    const [status, setStatus] = useState(filters.status);
    const [gender, setGender] = useState(filters.gender);
    const [showAddModal, setShowAddModal] = useState(false);

    useEffect(() => {
        dispatch(setPageTitle('Customers'));
    }, [dispatch]);

    const handleSearch = (value: string) => {
        setSearch(value);
        router.get(
            '/vendor/manage-customer',
            {
                search: value,
                status,
                gender,
                sort: filters.sort,
                direction: filters.direction
            },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleStatusChange = (value: string) => {
        setStatus(value);
        router.get(
            '/vendor/manage-customer',
            {
                search,
                status: value,
                gender,
                sort: filters.sort,
                direction: filters.direction
            },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleGenderChange = (value: string) => {
        setGender(value);
        router.get(
            '/vendor/manage-customer',
            {
                search,
                status,
                gender: value,
                sort: filters.sort,
                direction: filters.direction
            },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleSort = (field: string) => {
        const direction = filters.sort === field && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            '/vendor/manage-customer',
            {
                search,
                status,
                gender,
                sort: field,
                direction
            },
            { preserveState: true, preserveScroll: true }
        );
    };

    const getStatusBadgeClass = (isActive: boolean) => {
        return isActive
            ? 'badge badge-outline-success'
            : 'badge badge-outline-danger';
    };

    const getGenderBadgeClass = (gender: string) => {
        return gender === 'male'
            ? 'badge badge-outline-info'
            : 'badge badge-outline-warning';
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Customers</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex md:flex-row flex-col md:items-center gap-5 mb-5">
                        <div className="flex flex-1 items-center gap-5">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search customers..."
                                    className="py-2 ltr:pr-11 rtl:pl-11 form-input peer"
                                    value={search}
                                    onChange={(e) => handleSearch(e.target.value)}
                                />
                                <button type="button" className="top-1/2 ltr:right-[11px] rtl:left-[11px] absolute peer-focus:text-primary -translate-y-1/2">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="11.5" cy="11.5" r="9.5" stroke="currentColor" strokeWidth="1.5" opacity="0.5"></circle>
                                        <path d="M18.5 18.5L22 22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"></path>
                                    </svg>
                                </button>
                            </div>
                            <div className="flex items-center gap-5">
                                <select
                                    className="w-32 form-select"
                                    value={status}
                                    onChange={(e) => handleStatusChange(e.target.value)}
                                >
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                                <select
                                    className="w-32 form-select"
                                    value={gender}
                                    onChange={(e) => handleGenderChange(e.target.value)}
                                >
                                    <option value="">All Gender</option>
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                </select>
                            </div>
                        </div>
                        <div className="flex-none">
                            <button
                                className="btn btn-primary"
                                onClick={() => setShowAddModal(true)}
                                type="button"
                            >
                                + Add Customer
                            </button>
                        </div>
                    </div>

                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th onClick={() => handleSort('name')} className="cursor-pointer">
                                        Name {filters.sort === 'name' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th onClick={() => handleSort('email')} className="cursor-pointer">
                                        Email {filters.sort === 'email' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th onClick={() => handleSort('phone')} className="cursor-pointer">
                                        Phone {filters.sort === 'phone' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th>Gender</th>
                                    <th onClick={() => handleSort('appointments_count')} className="cursor-pointer">
                                        Total Bookings {filters.sort === 'appointments_count' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th>Status</th>
                                    <th onClick={() => handleSort('created_at')} className="cursor-pointer">
                                        Joined {filters.sort === 'created_at' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {customers.data.map((customer) => (
                                    <tr key={customer.id}>
                                        <td>
                                            <div>
                                                <div className="font-semibold">{customer.name}</div>
                                                {customer.company_name && (
                                                    <div className="text-gray-500 text-xs">{customer.company_name}</div>
                                                )}
                                            </div>
                                        </td>
                                        <td>{customer.email}</td>
                                        <td>{customer.phone || 'N/A'}</td>
                                        <td>
                                            <span className={getGenderBadgeClass(customer.gender)}>
                                                {customer.gender || 'N/A'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="badge badge-outline-primary">
                                                {customer.appointments_count} bookings
                                            </span>
                                        </td>
                                        <td>
                                            <span className={getStatusBadgeClass(customer.is_active)}>
                                                {customer.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </td>
                                        <td>
                                            {new Date(customer.created_at).toLocaleDateString()}
                                        </td>
                                        <td className="text-center">
                                            <div className="flex justify-center items-center gap-4">
                                                <Link
                                                    href={`/vendor/manage-customer/show/${customer.id}`}
                                                    className="btn btn-sm btn-outline-primary"
                                                >
                                                    View Details
                                                </Link>
                                                <Link
                                                    href={`/vendor/manage-customer/show/${customer.id}/booking-history`}
                                                    className="btn btn-sm btn-outline-info"
                                                >
                                                    Booking History
                                                </Link>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination */}
                    <div className="mt-6">
                        <Pagination links={customers.links} />
                    </div>
                </div>
            </div>

            <AddCustomerModal
                open={showAddModal}
                onClose={() => setShowAddModal(false)}
                onSuccess={() => {
                    setShowAddModal(false);
                    router.reload({ only: ['customers'] });
                }}
            />
        </div>
    );
};

export default Index;
