// Flag Images - using string paths for runtime resolution
const AESvg = '/assets/images/flags/AE.svg';
const BGSvg = '/assets/images/flags/BG.svg';
const CNSvg = '/assets/images/flags/CN.svg';
const DESvg = '/assets/images/flags/DE.svg';
const DKSvg = '/assets/images/flags/DK.svg';
const ESSvg = '/assets/images/flags/ES.svg';
const FRSvg = '/assets/images/flags/FR.svg';
const GBSvg = '/assets/images/flags/GB.svg';
const GRSvg = '/assets/images/flags/GR.svg';
const HUSvg = '/assets/images/flags/HU.svg';
const IDSvg = '/assets/images/flags/ID.svg';
const INSvg = '/assets/images/flags/IN.svg';
const ITSvg = '/assets/images/flags/IT.svg';
const JPSvg = '/assets/images/flags/JP.svg';
const NLSvg = '/assets/images/flags/NL.svg';
const PLSvg = '/assets/images/flags/PL.svg';
const PTSvg = '/assets/images/flags/PT.svg';
const RUSvg = '/assets/images/flags/RU.svg';
const SESvg = '/assets/images/flags/SE.svg';
const TRSvg = '/assets/images/flags/TR.svg';
const USSvg = '/assets/images/flags/US.svg';

export const flagImages = {
    AE: AESvg,
    BG: BGSvg,
    CN: CNSvg,
    DE: DESvg,
    DK: DKSvg,
    ES: ESSvg,
    FR: FRSvg,
    GB: GBSvg,
    GR: GRSvg,
    HU: HUSvg,
    ID: IDSvg,
    IN: INSvg,
    IT: ITSvg,
    JP: JPSvg,
    NL: NLSvg,
    PL: PLSvg,
    PT: PTSvg,
    RU: RUSvg,
    SE: SESvg,
    TR: TRSvg,
    US: USSvg,
};

// Helper function to get flag image by code
export const getFlagImage = (code: string): string => {
    const upperCode = code.toUpperCase() as keyof typeof flagImages;
    return flagImages[upperCode] || flagImages.US; // Default to US flag if not found
};

export default flagImages;
