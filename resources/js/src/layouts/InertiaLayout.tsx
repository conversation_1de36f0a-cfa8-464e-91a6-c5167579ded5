import { PropsWithChildren } from 'react';
import { usePage } from '@inertiajs/react';
import DefaultLayout from '@components/Layouts/DefaultLayout';
import BlankLayout from '@components/Layouts/BlankLayout';

interface InertiaLayoutProps {
  layout?: 'default' | 'blank';
}

const InertiaLayout = ({ children, layout }: PropsWithChildren<InertiaLayoutProps>) => {
  const { props } = usePage();
  const layoutType = layout || (props as any).layout || 'default';

  if (layoutType === 'blank') {
    return <BlankLayout>{children}</BlankLayout>;
  }

  return <DefaultLayout>{children}</DefaultLayout>;
};

export default InertiaLayout;
